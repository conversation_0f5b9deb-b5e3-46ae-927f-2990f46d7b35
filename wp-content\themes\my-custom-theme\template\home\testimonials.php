<!-- testimonial-area -->
<div class="testimonial-area bg py-90">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="site-heading text-center wow fadeInDown" data-wow-delay=".25s">
                    <span class="site-title-tagline">Testimonials</span>
                    <h2 class="site-title">What Our <span>Guest Say's</span> <br> About Us</h2>
                    <div class="site-shadow-text">Testimonials</div>
                </div>
            </div>
        </div>
        <div class="testimonial-slider owl-carousel owl-theme wow fadeInUp" data-wow-delay=".25s">
            <div class="testimonial-single">
                <div class="testimonial-quote">
                    <div class="testimonial-rate">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p>
                        There are many variations of passage available the majority have
                        suffered to alteration in some form making it look like readable by injected humour.
                    </p>
                    <div class="testimonial-quote-icon">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/img/icon/quote.svg" alt="">
                    </div>
                </div>
                <div class="testimonial-content">
                    <div class="testimonial-author-img">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/img/testimonial/01.jpg" alt="">
                    </div>
                    <div class="testimonial-author-info">
                        <h4>Anderson Dele</h4>
                        <p>Our Guest</p>
                    </div>
                </div>
            </div>
            <div class="testimonial-single">
                <div class="testimonial-quote">
                    <div class="testimonial-rate">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p>
                        There are many variations of passage available the majority have
                        suffered to alteration in some form making it look like readable by injected humour.
                    </p>
                    <div class="testimonial-quote-icon">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/img/icon/quote.svg" alt="">
                    </div>
                </div>
                <div class="testimonial-content">
                    <div class="testimonial-author-img">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/img/testimonial/02.jpg" alt="">
                    </div>
                    <div class="testimonial-author-info">
                        <h4>Gordon Novak</h4>
                        <p>Our Guest</p>
                    </div>
                </div>
            </div>
            <div class="testimonial-single">
                <div class="testimonial-quote">
                    <div class="testimonial-rate">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p>
                        There are many variations of passage available the majority have
                        suffered to alteration in some form making it look like readable by injected humour.
                    </p>
                    <div class="testimonial-quote-icon">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/img/icon/quote.svg" alt="">
                    </div>
                </div>
                <div class="testimonial-content">
                    <div class="testimonial-author-img">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/img/testimonial/03.jpg" alt="">
                    </div>
                    <div class="testimonial-author-info">
                        <h4>Lucille Rucker</h4>
                        <p>Our Guest</p>
                    </div>
                </div>
            </div>
            <div class="testimonial-single">
                <div class="testimonial-quote">
                    <div class="testimonial-rate">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p>
                        There are many variations of passage available the majority have
                        suffered to alteration in some form making it look like readable by injected humour.
                    </p>
                    <div class="testimonial-quote-icon">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/img/icon/quote.svg" alt="">
                    </div>
                </div>
                <div class="testimonial-content">
                    <div class="testimonial-author-img">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/img/testimonial/04.jpg" alt="">
                    </div>
                    <div class="testimonial-author-info">
                        <h4>Elizabeth Galvan</h4>
                        <p>Our Guest</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- testimonial-area end -->

<?php
// Video testimonials data - you can move this to functions.php or use custom fields
$video_testimonials = array(
    array(
        'video_file' => 'testimonial-video-1.mp4', // Video filename in media library
        'thumbnail' => 'testimonial-thumb-1.jpg',   // Thumbnail filename in media library
        'author_name' => 'Sarah Johnson'
    ),
    array(
        'video_file' => 'testimonial-video-2.mp4',
        'thumbnail' => 'testimonial-thumb-2.jpg',
        'author_name' => 'Michael Chen'
    ),
    array(
        'video_file' => 'testimonial-video-3.mp4',
        'thumbnail' => 'testimonial-thumb-3.jpg',
        'author_name' => 'Emily Rodriguez'
    ),
    array(
        'video_file' => 'testimonial-video-4.mp4',
        'thumbnail' => 'testimonial-thumb-4.jpg',
        'author_name' => 'David Thompson'
    )
);

// Function to get media URL by filename
function get_media_url_by_filename($filename) {
    $upload_dir = wp_upload_dir();

    // First try to find the attachment by filename
    $args = array(
        'post_type' => 'attachment',
        'post_status' => 'inherit',
        'meta_query' => array(
            array(
                'key' => '_wp_attached_file',
                'value' => $filename,
                'compare' => 'LIKE'
            )
        )
    );

    $attachments = get_posts($args);

    if ($attachments) {
        return wp_get_attachment_url($attachments[0]->ID);
    }

    // Fallback: construct URL manually (assumes file is in uploads root)
    return $upload_dir['baseurl'] . '/' . $filename;
}
?>

<!-- video-testimonial-area -->
<div class="video-testimonial-area bg py-90">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="site-heading text-center wow fadeInDown" data-wow-delay=".25s">
                    <span class="site-title-tagline">Video Testimonials</span>
                    <h2 class="site-title">Watch What Our <span>Guests Say</span> <br> About Their Experience</h2>
                    <div class="site-shadow-text">Videos</div>
                </div>
            </div>
        </div>
        <div class="video-testimonial-slider owl-carousel owl-theme wow fadeInUp" data-wow-delay=".25s">
            <?php foreach ($video_testimonials as $index => $testimonial):
                $video_url = get_media_url_by_filename($testimonial['video_file']);
                $thumbnail_url = get_media_url_by_filename($testimonial['thumbnail']);
            ?>
            <div class="testimonial-single video-testimonial">
                <div class="testimonial-video-wrapper">
                    <div class="video-thumbnail">
                        <img src="<?php echo esc_url($thumbnail_url); ?>" alt="Video Testimonial - <?php echo esc_attr($testimonial['author_name']); ?>">
                        <div class="video-play-btn" data-video-url="<?php echo esc_url($video_url); ?>" data-video-type="local">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="video-modal" style="display: none;">
                        <div class="video-modal-content">
                            <span class="video-close">&times;</span>
                            <video controls autoplay>
                                <source src="<?php echo esc_url($video_url); ?>" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    </div>
                </div>
                <div class="testimonial-content">
                    <div class="testimonial-author-info">
                        <h4><?php echo esc_html($testimonial['author_name']); ?></h4>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<!-- video-testimonial-area end -->