# Video Testimonials - WordPress Media Integration Guide

## 🎥 Overview
Your video testimonials now pull videos and thumbnails directly from the WordPress media library, making it easy to manage content through the WordPress admin.

## 📁 How to Add Videos

### Step 1: Upload Videos to WordPress Media Library
1. Go to **WordPress Admin → Media → Add New**
2. Upload your video testimonial files (MP4 format recommended)
3. Note the exact filename (e.g., `testimonial-video-1.mp4`)

### Step 2: Upload Thumbnail Images
1. In the same Media Library, upload thumbnail images for each video
2. Use JPG format, recommended size: 400x250 pixels
3. Note the exact filename (e.g., `testimonial-thumb-1.jpg`)

### Step 3: Update the Configuration
Edit the file: `wp-content/themes/my-custom-theme/template/home/<USER>

Find this section around line 130:
```php
$video_testimonials = array(
    array(
        'video_file' => 'testimonial-video-1.mp4', // Change this to your video filename
        'thumbnail' => 'testimonial-thumb-1.jpg',   // Change this to your thumbnail filename
        'author_name' => '<PERSON>'            // Change this to the person's name
    ),
    // Add more testimonials here...
);
```

## 📝 Configuration Examples

### Example 1: Basic Setup
```php
array(
    'video_file' => 'john-doe-testimonial.mp4',
    'thumbnail' => 'john-doe-thumb.jpg',
    'author_name' => 'John Doe'
)
```

### Example 2: Multiple Videos
```php
$video_testimonials = array(
    array(
        'video_file' => 'sarah-conference-review.mp4',
        'thumbnail' => 'sarah-thumbnail.jpg',
        'author_name' => 'Sarah Johnson'
    ),
    array(
        'video_file' => 'michael-business-testimonial.mp4',
        'thumbnail' => 'michael-thumb.jpg',
        'author_name' => 'Michael Chen'
    ),
    array(
        'video_file' => 'emily-expert-review.mp4',
        'thumbnail' => 'emily-thumbnail.jpg',
        'author_name' => 'Emily Rodriguez'
    )
);
```

## 🔧 Technical Details

### File Locations
- **Videos**: Stored in WordPress uploads folder (usually `/wp-content/uploads/`)
- **Thumbnails**: Also stored in WordPress uploads folder
- **Configuration**: `wp-content/themes/my-custom-theme/template/home/<USER>

### Supported Video Formats
- MP4 (recommended)
- WebM
- OGV

### How It Works
1. The `get_media_url_by_filename()` function searches WordPress media library for files by name
2. If found, it returns the proper WordPress media URL
3. If not found, it falls back to a direct URL construction
4. Videos play in a modal popup with HTML5 video player

## 🎨 Features
- ✅ Single video slider (cycles one at a time)
- ✅ Auto-play when modal opens
- ✅ Responsive design
- ✅ Professional video controls
- ✅ Multiple ways to close modal (X, ESC, click outside)
- ✅ Prevents background scrolling during video playback

## 🚀 Adding More Videos
To add more testimonials, simply:
1. Upload new video and thumbnail to WordPress Media
2. Add a new array entry to the `$video_testimonials` array
3. The slider will automatically include the new video

## 📱 Mobile Optimization
- Videos are responsive and work on all devices
- Touch-friendly controls
- Optimized for mobile data usage

## 🔍 Troubleshooting
- **Video not showing**: Check that the filename exactly matches what's in WordPress Media Library
- **Thumbnail not loading**: Verify thumbnail filename and that image was uploaded to Media Library
- **Video won't play**: Ensure video is in MP4 format and properly uploaded

## 💡 Pro Tips
- Keep video files under 50MB for better loading times
- Use 16:9 aspect ratio for videos
- Compress videos before uploading to reduce file size
- Test on mobile devices to ensure smooth playback
