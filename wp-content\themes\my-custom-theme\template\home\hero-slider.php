<?php

/**
 * Template part for displaying hero slider on home page
 */
?>

<!-- hero slider -->
<div class="hero-section">
    <div class="hero-scroll-box">
        <div class="hero-scroll">
            <div class="scroller"></div>
        </div>
    </div>
    <div class="hero-slider owl-carousel owl-theme">
        <?php
        // Example: Using hardcoded slides (replace with dynamic content in next step)
        $slides = [
            [
                'image' => get_template_directory_uri() . '/assets/img/hero/slider-1.jpg',
                'date' => 'October 1st - 4th',
                'location' => 'Atlanta, GA, USA',
                'title' => 'Connect. Trade. <span>Grow Globally</span>.',
                'desc' => 'Expand your network with business owners, investors, and trade leaders from across the world.',
            ],
            [
                'image' => get_template_directory_uri() . '/assets/img/hero/slider-2.jpg',
                'date' => 'October 1st - 4th',
                'location' => 'Atlanta, GA, USA',
                'title' => 'Meet Markets. <span>Make Deals</span>',
                'desc' => 'Meet directly with decision-makers, suppliers, and investors aligned with your goals.',
            ],
            [
                'image' => get_template_directory_uri() . '/assets/img/hero/slider-3.jpg',
                'date' => 'October 1st - 4th',
                'location' => 'Atlanta, GA, USA',
                'title' => 'Step Into <span>Global Growth.</span>',
                'desc' => 'This is your chance to step into new markets, industries, and opportunities.',
            ],
        ];

        foreach ($slides as $slide) : ?>
            <div class="hero-single" style="background: url(<?php echo esc_url($slide['image']); ?>)">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-lg-7">
                            <div class="hero-content">
                                <div class="hero-date" data-animation="fadeInDown" data-delay=".25s">
                                    <h1>25</h1>
                                    <div class="date-content">
                                        <span><?php echo esc_html($slide['date']); ?></span>
                                        <p><?php echo esc_html($slide['location']); ?></p>
                                    </div>
                                </div>
                                <h1 class="hero-title" data-animation="fadeInRight" data-delay=".50s"><?php echo $slide['title']; ?></h1>
                                <p data-animation="fadeInLeft" data-delay=".75s"><?php echo esc_html($slide['desc']); ?></p>
                                <div class="hero-btn" data-animation="fadeInUp" data-delay="1s">
                                    <a href="#" class="theme-btn">Buy Ticket<i class="fas fa-arrow-right"></i></a>
                                    <a href="#" class="theme-btn theme-btn3">B2B Matchmaking<i class="fas fa-arrow-right"></i></a>
                                    <a href="#" class="theme-btn theme-btn4">Become an Exhibitor<i class="fas fa-arrow-right"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<!-- hero slider end -->