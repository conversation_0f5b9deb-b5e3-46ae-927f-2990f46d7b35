/*=====================================================
Template Name   : Eventu
Description     : Event And Conference HTML5 Template
Author          : LunarTemp
Version         : 1.0
=======================================================*/


/*=====================================================
Table of contents
=======================================================
1. Google fonts
2. Theme variables
3. General css
4. Preloader
5. Theme default css
6. Margin & padding
7. Site title css
8. Theme button
9. Container
10. Scroll top css
11. Header
12. Header top css
13. Navbar css
14. Multi level dropdown menu
15. Search popup
16. Sidebar popup css 
17. Main section css 
18. Hero css 
19. Play btn
20. Event countdown css
21. About css 
22. Feature css 
23. Schedule css 
24. Schedule single css 
25. Pricing css 
26. Video css 
27. Counter css
28. Team css 
29. Team single css 
30. Cta css 
31. Choose css 
32. Venue css
33. Venue single css 
34. Testimonial css
35. Quote css 
36. Instagram css 
37. Blog css 
38. Blog single css
39. Widget sidebar css
40. Contact us css 
41. Gallery css 
42. Faq css 
43. Breadcrumb css
44. Pagination css 
45. Auth css 
46. Partner css 
47. Coming soon css 
48. Error css 
49. Terms/privacy css 
50. Footer css 
51. Home 2
52. Home 3
=======================================================*/


/*====================
1. Google fonts
======================*/

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800;900&family=Roboto:wght@100;300;400;500;700;900&display=swap');


/*====================
2. Theme variables
======================*/

:root {
  --body-font: 'Roboto', sans-serif;
  --heading-font: "Poppins", sans-serif;
  --theme-color: #EEA90B;
  --theme-color2: #0D2B3F;
  --theme-bg-light: #F8F9FE;
  --color-dark: #01054C;
  --color-gray: #F6F6F6;
  --body-text-color: #757F95;
  --color-white: #ffffff;
  --hero-overlay: rgb(5, 3, 17);
  --slider-arrow-bg: rgba(140, 82, 255, .2);
  --box-shadow: 0 0 40px 5px rgb(0 0 0 / 5%);
  --box-shadow2: 0 0 15px rgba(0, 0, 0, 0.17);
  --transition: all .5s ease-in-out;
  --transition2: all .3s ease-in-out;
  --border-info-color: rgba(0, 0, 0, 0.08);
  --border-info-color2: rgba(0, 0, 0, 0.05);
  --border-white-color: rgba(255, 255, 255, 0.08);
  --border-white-color2: rgba(255, 255, 255, 0.05);
  --footer-bg: #01103B;
  --footer-bg2: #00134C;
  --footer-text-color: #F5FAFF;
  --theme-gradient: linear-gradient(to right, #EEA90B 0%, #EEA90B 100%);
}



/*====================
3. General css
======================*/

*,
*:before,
*:after {
  box-sizing: inherit;
}

* {
  scroll-behavior: inherit !important;
}

html,
body {
  height: auto;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  box-sizing: border-box;
}

body {
  font-family: var(--body-font);
  font-style: normal;
  font-size: 16px;
  font-weight: normal;
  color: var(--body-text-color);
  line-height: 1.8;
}

a {
  color: var(--color-dark);
  display: inline-block;
}

a,
a:active,
a:focus,
a:hover {
  outline: none;
  -webkit-transition: all 0.3s ease-out 0s;
  -moz-transition: all 0.3s ease-out 0s;
  -o-transition: all 0.3s ease-out 0s;
  -ms-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
  text-decoration: none;
}

a:hover {
  color: var(--color-blue);
}

ul {
  margin: 0;
  padding: 0;
}

li {
  list-style: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--color-dark);
  margin: 0px;
  font-weight: 600;
  font-family: var(--heading-font);
  line-height: 1.2;
}

h1 {
  font-size: 40px;
}

h2 {
  font-size: 35px;
}

h3 {
  font-size: 28px;
}

h4 {
  font-size: 22px;
}

h5 {
  font-size: 18px;
}

h6 {
  font-size: 16px;
}

p {
  margin: 0px;
}

.img,
img {
  max-width: 100%;
  -webkit-transition: all 0.3s ease-out 0s;
  -moz-transition: all 0.3s ease-out 0s;
  -ms-transition: all 0.3s ease-out 0s;
  -o-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
  height: auto;
}

label {
  color: #999;
  cursor: pointer;
  font-weight: 400;
}

*::-moz-selection {
  background: #d6b161;
  color: var(--color-white);
  text-shadow: none;
}

::-moz-selection {
  background: #555;
  color: var(--color-white);
  text-shadow: none;
}

::selection {
  background: #555;
  color: var(--color-white);
  text-shadow: none;
}

*::-moz-placeholder {
  color: #999;
  font-size: 16px;
  opacity: 1;
}

*::placeholder {
  color: #999;
  font-size: 16px;
  opacity: 1;
}



/*====================
4. Preloader
======================*/

.preloader {
  position: fixed;
  width: 100%;
  height: 100%;
  background: var(--color-white);
  top: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader-ripple {
  display: inline-block;
  position: relative;
  width: 80px;
  height: 80px;
}

.loader-ripple div {
  position: absolute;
  border: 4px solid var(--theme-color);
  opacity: 1;
  border-radius: 50%;
  animation: loader-ripple 1s cubic-bezier(0, 0.2, 0.8, 1) infinite;
}

.loader-ripple div:nth-child(2) {
  animation-delay: -0.5s;
}

@keyframes loader-ripple {
  0% {
    top: 36px;
    left: 36px;
    width: 0;
    height: 0;
    opacity: 1;
  }

  100% {
    top: 0px;
    left: 0px;
    width: 72px;
    height: 72px;
    opacity: 0;
  }
}




/*===================
5. Theme default css
======================*/

.ovrflow-hidden {
  overflow: hidden;
}

.position-relative {
  position: relative;
  z-index: 1;
}

.text-right {
  text-align: right;
}

.space-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.c-pd {
  padding: 0 7rem;
}

.s-pd {
  padding: 0 12rem;
}

.h-100 {
  height: 100%;
}

.h-100vh {
  height: 100vh;
}

.bg {
  background: var(--theme-bg-light);
}



/*====================
6. Margin & padding
======================*/

.pt-0 {
  padding-top: 0px;
}

.pt-10 {
  padding-top: 10px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-50 {
  padding-top: 50px;
}

.pt-60 {
  padding-top: 60px;
}

.pt-70 {
  padding-top: 70px;
}

.pt-80 {
  padding-top: 80px;
}

.pt-90 {
  padding-top: 90px;
}

.pt-100 {
  padding-top: 100px;
}

.pt-110 {
  padding-top: 110px;
}

.pt-120 {
  padding-top: 120px;
}

.pb-0 {
  padding-bottom: 0px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pb-70 {
  padding-bottom: 70px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pb-90 {
  padding-bottom: 90px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pb-110 {
  padding-bottom: 110px;
}

.pb-120 {
  padding-bottom: 120px;
}

.py-80 {
  padding: 80px 0;
}

.py-90 {
  padding: 90px 0;
}

.py-100 {
  padding: 100px 0;
}

.py-110 {
  padding: 110px 0;
}

.py-120 {
  padding: 120px 0;
}

.mt-0 {
  margin-top: 0px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-60 {
  margin-top: 60px;
}

.mt-70 {
  margin-top: 70px;
}

.mt-80 {
  margin-top: 80px;
}

.mt-90 {
  margin-top: 90px;
}

.mt-100 {
  margin-top: 100px;
}

.mt-110 {
  margin-top: 110px;
}

.mt-120 {
  margin-top: 120px;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-60 {
  margin-bottom: 60px;
}

.mb-70 {
  margin-bottom: 70px;
}

.mb-80 {
  margin-bottom: 80px;
}

.mb-90 {
  margin-bottom: 90px;
}

.mb-100 {
  margin-bottom: 100px;
}

.mb-110 {
  margin-bottom: 110px;
}

.mb-120 {
  margin-bottom: 120px;
}

.my-80 {
  margin: 80px 0;
}

.my-90 {
  margin: 90px 0;
}

.my-100 {
  margin: 100px 0;
}

.my-110 {
  margin: 110px 0;
}

.my-120 {
  margin: 120px 0;
}



/*====================
7. Site title css
======================*/

.site-heading {
  margin-bottom: 50px;
  position: relative;
  z-index: 1;
}

.site-title-tagline {
  display: block;
  font-family: var(--heading-font2);
  text-transform: uppercase;
  letter-spacing: 4px;
  font-size: 22px;
  font-weight: 700;
  color: var(--theme-color);
  margin-bottom: 8px;
  position: relative;
}

.site-title-tagline i {
  line-height: 0;
  font-size: 21px;
}

.site-title {
  font-weight: 700;
  text-transform: capitalize;
  font-size: 36px;
  color: var(--color-dark);
  margin-bottom: 0;
}

.site-title span {
  color: var(--theme-color);
  font-weight: 500;
}

.site-heading p {
  margin-top: 15px;
}

.site-shadow-text {
  position: absolute;
  right: 0px;
  top: 0px;
  line-height: 0;
  font-size: 100px;
  font-family: var(--heading-font2);
  font-weight: bold;
  color: var(--color-gray);
  text-transform: uppercase;
  z-index: -1;
}

.heading-divider {
  display: inline-block;
  position: relative;
  border-bottom: 4px solid var(--theme-color);
  width: 90px;
  height: 4px;
}

.heading-divider:after {
  content: '';
  position: absolute;
  left: 0;
  top: -1px;
  height: 6px;
  width: 15px;
  border-radius: 0px;
  background-color: var(--color-white);
  -webkit-animation: heading-move 5s infinite linear;
  animation: heading-move 5s infinite linear;
}

@-webkit-keyframes heading-move {
  0% {
    transform: translateX(-1px);
  }

  50% {
    transform: translateX(75px);
  }

  100% {
    transform: translateX(-1px);
  }
}

@keyframes heading-move {
  0% {
    transform: translateX(-1px);
  }

  50% {
    transform: translateX(75px);
  }

  100% {
    transform: translateX(-1px);
  }
}

@media all and (max-width: 991px) {
  .site-shadow-text {
    line-height: 1;
    top: -70px !important;
  }
}

@media all and (max-width: 767px) {
  .site-shadow-text {
    font-size: 65px !important;
    top: -40px !important;
  }
}




/*====================
8. Theme button
======================*/

.theme-btn {
  font-size: 16px;
  color: var(--color-white);
  padding: 10px 24px;
  transition: all 0.5s;
  text-transform: capitalize;
  position: relative;
  border-radius: 50px;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
  border: none;
  background: var(--theme-gradient);
  box-shadow: var(--box-shadow);
  z-index: 1;
}

.theme-btn::before {
  content: "";
  height: 300px;
  width: 300px;
  background: var(--theme-color2);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateY(-50%) translateX(-50%) scale(0);
  transition: 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: -1;
}

.theme-btn:hover {
  color: var(--color-white);
}

.theme-btn:hover::before {
  transform: translateY(-50%) translateX(-50%) scale(1);
}

.theme-btn i {
  margin-left: 10px;
  transform: rotate(-35deg);
  transition: var(--transition2);
}

.theme-btn:hover i {
  transform: rotate(0);
}

.theme-btn span {
  margin-right: 5px;
}

.theme-btn2 {
  background: var(--color-white);
  color: var(--theme-color);
}

.theme-btn2::before {
  background: var(--theme-color);
}

.theme-btn2:hover {
  color: var(--color-white);
}



/*====================
9. Container
======================*/

@media (min-width: 1200px) {

  .container,
  .container-sm,
  .container-md,
  .container-lg,
  .container-xl {
    max-width: 1200px;
  }
}



/*====================
10. Scroll top css
======================*/

#scroll-top {
  position: fixed;
  bottom: -20px;
  right: 30px;
  z-index: 99;
  font-size: 20px;
  border: none;
  outline: none;
  border-radius: 50px;
  color: var(--color-white);
  background-color: var(--theme-color);
  cursor: pointer;
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  box-shadow: var(--box-shadow2);
  transition: var(--transition);
  opacity: 0;
  visibility: hidden;
  z-index: 1;
}

#scroll-top i {
  transform: rotate(-35deg);
  transition: var(--transition2);
}

#scroll-top:hover i {
  transform: rotate(0);
}

#scroll-top.active {
  opacity: 1;
  visibility: visible;
  bottom: 20px;
}


@media all and (min-width: 768px) and (max-width: 1199px) {
  #scroll-top.active {
    bottom: 100px;
  }
}



/*====================
11. Header
======================*/
.header {
  background: var(--theme-gradient);
}




/*====================
12. Header top css
======================*/

.header-top {
  position: relative;
  padding: 14px 0 14px 0;
  z-index: 5;
}

.header-top-contact ul {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.header-top-contact a {
  color: var(--color-white);
  font-weight: 500;
}

.header-top-contact a i {
  color: var(--color-white);
}

.header-top-middle {
  text-align: center;
}

.header-top-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-wrap: wrap;
  gap: 15px;
}

.header-top-link a {
  color: var(--color-white);
  margin-right: 12px;
}

.header-top-link a:hover {
  color: var(--theme-color);
}

.header-top-social span {
  color: var(--color-white);
}

.header-top-social a {
  color: var(--color-white);
  font-size: 16px;
  text-align: center;
  margin-left: 14px;
  transition: var(--transition);
}

.header-top-social a:hover {
  color: var(--theme-color2);
}

.header-top-lang .top-lang {
  color: var(--color-white);
}

.header-top-lang .dropdown-menu {
  min-width: 60px;
  border-radius: 15px;
  padding: 10px;
  border: none;
  box-shadow: var(--box-shadow);
}

.header-top-lang .dropdown-item {
  color: var(--color-dark);
  border-radius: 10px;
}

.header-top-lang .dropdown-item:hover {
  background: var(--theme-color);
  color: var(--color-white);
}

@media all and (max-width: 1199px) {
  .header-top-contact li:first-child {
    display: none;
  }
}

@media all and (max-width: 991px) {
  .header-top {
    padding-top: 12px;
  }

  .header-top-contact ul,
  .header-top-right {
    justify-content: center;
  }
}



/*====================
13. Navbar css
======================*/

.navbar {
  background: transparent;
  padding-top: 0px;
  padding-bottom: 0px;
  z-index: 4;
  position: relative;
}

.navbar::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: calc(100% - 100px);
  height: 100%;
  background: var(--theme-color2);
}

.navbar.fixed-top {
  position: fixed;
  background: var(--theme-color2);
  box-shadow: var(--box-shadow2);
  animation: slide-down 0.7s;
}


@keyframes slide-down {
  0% {
    transform: translateY(-100%);
  }

  100% {
    transform: translateY(0);
  }
}

.navbar .navbar-brand .logo-display {
  display: block;
}

.navbar .navbar-brand .logo-scrolled {
  display: none;
}

.navbar.fixed-top .navbar-brand .logo-display {
  display: none;
}

.navbar.fixed-top .navbar-brand .logo-scrolled {
  display: block;
}

.navbar .navbar-toggler:focus {
  outline: none;
  box-shadow: none;
}

.navbar-toggler-mobile-icon {
  display: inline-block;
  width: inherit;
  height: inherit;
}

.navbar-brand {
  margin-right: 0;
}

.navbar-brand img {
  width: 170px;
}

.navbar .dropdown-toggle::after {
  display: inline-block;
  margin-left: 5px;
  vertical-align: baseline;
  font-family: 'Font Awesome 6 Pro';
  content: "\f107";
  font-weight: 600;
  border: none;
  font-size: 14px;
}

@media all and (max-width: 1199px) {
  .navbar::before {
    width: 93%;
  }

  .nav-right {
    margin-left: 25px !important;
  }

  .navbar .nav-item .nav-link {
    margin-right: 15px;
  }

  .navbar .nav-right-btn {
    display: none;
  }
}

@media all and (min-width: 992px) {
  .navbar .nav-item .nav-link {
    margin-right: 25px;
    padding: 25px 0 25px 0;
    font-size: 17px;
    font-weight: 500;
    color: #ffffff;
    text-transform: capitalize;
  }

  .navbar .nav-item:last-child .nav-link {
    margin-right: 0;
  }

  .navbar .nav-item .dropdown-menu {
    display: block;
    padding: 10px;
    margin-top: 0;
    left: -15px;
    border-radius: 15px;
    border: none;
    background: var(--color-white);
    width: 220px;
    box-shadow: var(--box-shadow);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition2);
  }

  .navbar .nav-item .dropdown-menu .dropdown-item {
    font-size: 16px;
    padding: 6px 15px;
    font-weight: 500;
    color: var(--color-dark);
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    text-transform: capitalize;
    transition: var(--transition2);
    z-index: 1;
  }

  .navbar .nav-item .dropdown-menu .dropdown-item:hover {
    background: var(--theme-color);
    color: var(--color-white);
    padding-left: 32px;
  }

  .navbar .nav-item .dropdown-menu .dropdown-item::before {
    content: "//";
    position: absolute;
    left: 15px;
    top: 6px;
    color: var(--color-white);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: -1;
  }

  .navbar .nav-item .dropdown-menu .dropdown-item:hover::before {
    opacity: 1;
    visibility: visible;
  }

  .navbar .nav-item .nav-link {
    position: relative;
  }

  .navbar .nav-item .nav-link::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 20px;
    width: 0;
    height: 2px;
    border-radius: 50px;
    transition: var(--transition2);
  }

  .navbar .nav-item .nav-link.active::before,
  .navbar .nav-item:hover .nav-link::before {
    background: var(--theme-gradient);
    width: 40%;
  }

  .navbar.fixed-top .nav-item .nav-link.active::before,
  .navbar.fixed-top .nav-item:hover .nav-link::before {
    background: var(--color-white);
  }

  .navbar .nav-item .nav-link.active,
  .navbar .nav-item:hover .nav-link {
    color: var(--theme-color);
  }

  .navbar .nav-item:hover .dropdown-menu {
    transition: .3s;
    opacity: 1;
    visibility: visible;
    top: 100%;
    transform: rotateX(0deg);
  }

  .navbar .dropdown-menu-end {
    right: 0;
    left: auto;
  }

  .navbar .dropdown-menu.fade-down {
    top: 80%;
    transform: rotateX(-75deg);
    transform-origin: 0% 0%;
  }

  .navbar .dropdown-menu.fade-up {
    top: 140%;
  }

  .navbar #main_nav {
    justify-content: end;
  }

  /* nav right */
  .nav-right {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 28px;
    margin-left: 45px;
  }

  .nav-right-link {
    position: relative;
    font-size: 20px;
    color: var(--color-dark);
    transition: var(--transition);
  }

  .nav-right-link:hover {
    color: var(--theme-color) !important;
  }

  .nav-right .search-btn .nav-right-link {
    border: none;
    background: transparent;
    color: var(--color-dark);
    font-size: 28px;
    padding-right: 0;
  }

  .nav-right .sidebar-btn .nav-right-link {
    background: var(--theme-color);
    width: 65px;
    height: 61px;
    color: var(--color-white) !important;
    font-size: 25px;
    border: none;
  }

  .nav-right .sidebar-btn .nav-right-link:hover {
    background: var(--theme-color2);
  }

  .nav-right .search-btn .nav-right-link {
    font-size: 20px;
    padding: 0;
  }
}


/* mobile menu */
.mobile-menu-right {
  display: none;
}

@media all and (max-width: 991px) {
  .navbar {
    position: absolute;
    width: 100%;
    height: auto;
    background: var(--color-white);
  }

  .navbar::before {
    display: none;
  }

  .navbar-brand img {
    width: 130px;
  }

  .navbar-collapse {
    max-height: 220px;
    overflow: hidden;
    overflow-y: auto;
    padding: 0 20px;
    margin-bottom: 10px;
    background-color: var(--theme-bg-light);
    border-radius: 15px;
  }

  .navbar-toggler {
    padding: 0;
    border: none;
  }

  .navbar .dropdown-toggle::after {
    float: right;
    margin-top: 2.5px;
  }

  .navbar .nav-item .nav-link {
    color: var(--color-dark);
    font-weight: 700;
    margin-right: 0px;
    transition: var(--transition);
  }

  .navbar .nav-item .nav-link:hover {
    color: var(--theme-color) !important;
  }

  .mobile-menu-right {
    margin: 0 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
  }

  .mobile-menu-right .nav-right-link {
    background: transparent;
    border: none;
    font-size: 20px;
    color: var(--color-dark);
  }

  .mobile-menu-right .nav-right-link:hover {
    color: var(--theme-color);
  }

  .navbar-toggler-mobile-icon {
    font-size: 25px;
    color: var(--color-dark);
    font-weight: 500;
  }

  .navbar .dropdown-menu {
    border-radius: 15px;
    border: none;
  }

  .nav-right {
    display: none;
  }
}


/*============================
14. Multi level dropdown menu
==============================*/

.navbar .nav-item .dropdown-submenu {
  position: relative;
}

.navbar .nav-item .dropdown-submenu .dropdown-menu::before {
  display: none;
}

.navbar .nav-item .dropdown-submenu a::after {
  transform: rotate(-90deg);
  position: absolute;
  right: 15px;
  top: 8px;
  font-weight: 600;
}

.navbar .nav-item .dropdown-submenu a:hover {
  background: transparent;
  color: var(--color-white);
}

.navbar .nav-item .dropdown-submenu .dropdown-menu {
  top: 120%;
  left: 100%;
  opacity: 0;
  visibility: hidden;
}

.navbar .nav-item .dropdown-submenu:hover .dropdown-menu {
  top: 0;
  opacity: 1;
  visibility: visible;
}

@media all and (max-width: 991px) {
  .navbar .nav-item .dropdown-menu .dropdown-item {
    color: var(--color-dark)
  }

  .navbar .nav-item .dropdown-submenu .dropdown-menu {
    margin: 0 17px;
  }

  .navbar .nav-item .dropdown-submenu .dropdown-menu {
    opacity: unset;
    visibility: unset;
  }

  .navbar .nav-item .dropdown-submenu a::after {
    top: 3px;
  }

  .navbar .nav-item .dropdown-submenu a:hover {
    color: var(--theme-color);
  }
}



/* ======================
15. Search popup
====================== */

.search-popup {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 100%;
  z-index: 99999;
  margin-top: -540px;
  transform: translateY(-100%);
  background-color: rgba(0, 0, 0, .8);
  transition: all 1500ms cubic-bezier(0.860, 0.000, 0.070, 1.000);
  transition-timing-function: cubic-bezier(0.860, 0.000, 0.070, 1.000);
}

.sidenav-bar-visible .search-popup {
  width: 80%;
}

.search-active .search-popup {
  transform: translateY(0%);
  margin-top: 0;
}

.search-popup .close-search {
  position: absolute;
  left: 0;
  right: 0;
  top: 75%;
  border: none;
  margin: 0 auto;
  margin-top: -200px;
  border-radius: 50px;
  text-align: center;
  background: var(--theme-color);
  text-align: center;
  width: 50px;
  height: 50px;
  color: var(--color-white);
  font-size: 20px;
  cursor: pointer;
  box-shadow: var(--box-shadow);
  transition: all 500ms ease;
  opacity: 0;
  visibility: hidden;
}

.search-active .search-popup .close-search {
  visibility: visible;
  opacity: 1;
  top: 50%;
  transition-delay: 1500ms;
}

.search-popup form {
  position: absolute;
  max-width: 700px;
  top: 50%;
  left: 15px;
  right: 15px;
  margin: -35px auto 0;
  transform: scaleX(0);
  transform-origin: center;
  transition: all 300ms ease;
}

.search-active .search-popup form {
  transform: scaleX(1);
  transition-delay: 1200ms;
}

.search-popup .form-group {
  position: relative;
  margin: 0px;
  overflow: hidden;
}

.search-popup .form-group input[type="text"],
.search-popup .form-group input[type="search"] {
  position: relative;
  width: 100%;
  height: 60px;
  outline: none;
  border-radius: 50px;
  border: none;
  padding: 0 70px 0 35px;
  transition: all 500ms ease;
  text-transform: capitalize;
}

.search-popup .form-group input[type="submit"],
.search-popup .form-group button {
  position: absolute;
  right: 5px;
  top: 5px;
  border-radius: 50px;
  background: var(--theme-color);
  text-align: center;
  font-size: 20px;
  color: var(--color-white) !important;
  height: 50px;
  width: 50px;
  border: none;
  cursor: pointer;
  transition: all 500ms ease;
}

.search-popup .form-group input[type="submit"]:hover,
.search-popup .form-group button:hover {
  background: var(--theme-color);
}

.search-popup input::placeholder,
.search-popup textarea::placeholder {
  color: var(--color-dark);
}



/*====================
16. Sidebar popup css 
======================*/

.sidebar-popup {
  position: fixed;
  top: 0;
  left: 0;
  content: "";
  background-color: rgba(0, 0, 0, 0.8);
  width: 100%;
  height: 100%;
  z-index: 999;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: var(--transition);
  transition: var(--transition);
}

.sidebar-popup.open {
  visibility: visible;
  opacity: 1;
}

.sidebar-wrapper {
  position: fixed;
  top: 0;
  right: -100%;
  width: 400px;
  background: var(--color-white);
  visibility: hidden;
  opacity: 0;
  padding: 40px;
  -webkit-transition: var(--transition);
  transition: var(--transition);
  z-index: 9999;
}

.sidebar-wrapper.open {
  right: 0;
  visibility: visible;
  opacity: 1;
}

.sidebar-content {
  position: relative;
  overflow-y: auto;
  height: calc(100vh - 75px);
}

.close-sidebar-popup {
  position: absolute;
  top: 1px;
  right: 1px;
  width: 38px;
  height: 38px;
  line-height: 36px;
  border-radius: 50px;
  text-align: center;
  border: none;
  font-size: 20px;
  background: var(--theme-color);
  color: var(--color-white);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.close-sidebar-popup:hover {
  background: var(--theme-color2);
}

.close-sidebar-popup i {
  transition: var(--transition2);
}

.close-sidebar-popup:hover i {
  transform: rotate(180deg);
}

.sidebar-logo img {
  width: 150px;
}

.sidebar-about {
  margin-top: 40px;
}

.sidebar-about h4 {
  margin-bottom: 10px;
}

.sidebar-contact {
  margin-top: 20px;
}

.sidebar-contact h4 {
  margin-bottom: 15px;
}

.sidebar-contact li {
  margin: 10px 0;
}

.sidebar-contact li i {
  margin-right: 5px;
  color: var(--theme-color);
}

.sidebar-contact li a:hover {
  color: var(--theme-color);
}

.sidebar-social {
  margin-top: 25px;
}

.sidebar-social h4 {
  margin-bottom: 20px;
}

.sidebar-social a {
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50px;
  margin-right: 8px;
  background: var(--theme-color);
  color: var(--color-white);
  box-shadow: var(--box-shadow);
}

.sidebar-social a:hover {
  background: var(--theme-color2);
}



/*====================
16.5. PopupMaker Notification Boxes
======================*/

/* Base notification styles using Eventu theme variables */
.pum-container,
.eventu-notification {
  font-family: var(--body-font);
  position: relative;
  z-index: 99999;
}

/* Notification box base styles */
.eventu-notification-box {
  position: fixed;
  top: 20px;
  right: 20px;
  max-width: 400px;
  min-width: 300px;
  background: var(--color-white);
  border-radius: 10px;
  box-shadow: var(--box-shadow2);
  border-left: 4px solid var(--theme-color);
  padding: 20px;
  z-index: 99999;
  transform: translateX(100%);
  opacity: 0;
  transition: var(--transition);
  overflow: hidden;
}

.eventu-notification-box.show {
  transform: translateX(0);
  opacity: 1;
}

.eventu-notification-box.hide {
  transform: translateX(100%);
  opacity: 0;
}

/* Notification types */
.eventu-notification-box.success {
  border-left-color: #28a745;
}

.eventu-notification-box.error {
  border-left-color: #dc3545;
}

.eventu-notification-box.warning {
  border-left-color: #ffc107;
}

.eventu-notification-box.info {
  border-left-color: var(--theme-color);
}

/* Notification header */
.eventu-notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.eventu-notification-title {
  font-family: var(--heading-font);
  font-size: 16px;
  font-weight: 600;
  color: var(--color-dark);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.eventu-notification-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: var(--color-white);
  background: var(--theme-color);
}

.eventu-notification-box.success .eventu-notification-icon {
  background: #28a745;
}

.eventu-notification-box.error .eventu-notification-icon {
  background: #dc3545;
}

.eventu-notification-box.warning .eventu-notification-icon {
  background: #ffc107;
}

/* Close button */
.eventu-notification-close {
  background: none;
  border: none;
  font-size: 18px;
  color: var(--body-text-color);
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--transition2);
}

.eventu-notification-close:hover {
  background: var(--color-gray);
  color: var(--color-dark);
}

/* Notification content */
.eventu-notification-content {
  color: var(--body-text-color);
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

/* Progress bar for auto-dismiss */
.eventu-notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: var(--theme-color);
  border-radius: 0 0 10px 10px;
  transition: width linear;
}

.eventu-notification-box.success .eventu-notification-progress {
  background: #28a745;
}

.eventu-notification-box.error .eventu-notification-progress {
  background: #dc3545;
}

.eventu-notification-box.warning .eventu-notification-progress {
  background: #ffc107;
}

/* PopupMaker specific overrides */
.pum-overlay {
  background-color: rgba(0, 0, 0, 0.8) !important;
  z-index: 99998 !important;
}

.pum-container {
  border-radius: 10px !important;
  box-shadow: var(--box-shadow2) !important;
  overflow: hidden !important;
}

.pum-content {
  font-family: var(--body-font) !important;
  color: var(--body-text-color) !important;
  background: var(--color-white) !important;
  padding: 30px !important;
  border-radius: 10px !important;
}

.pum-title {
  font-family: var(--heading-font) !important;
  color: var(--color-dark) !important;
  font-weight: 600 !important;
  margin-bottom: 15px !important;
  border-bottom: 2px solid var(--theme-color) !important;
  padding-bottom: 10px !important;
}

/* PopupMaker close button styling */
.pum-close {
  background: var(--theme-color) !important;
  color: var(--color-white) !important;
  border-radius: 50% !important;
  width: 30px !important;
  height: 30px !important;
  line-height: 28px !important;
  text-align: center !important;
  font-size: 16px !important;
  transition: var(--transition2) !important;
  border: none !important;
  cursor: pointer !important;
}

.pum-close:hover {
  background: var(--theme-color2) !important;
  transform: rotate(90deg) !important;
}

/* Responsive notification positioning */
@media (max-width: 768px) {
  .eventu-notification-box {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
    min-width: auto;
    transform: translateY(-100%);
  }

  .eventu-notification-box.show {
    transform: translateY(0);
  }

  .eventu-notification-box.hide {
    transform: translateY(-100%);
  }
}

/*====================
17. Main section css
======================*/

.main {
  margin-top: -2.5rem;
}

@media all and (max-width: 991px) {
  .main {
    margin-top: 0rem;
  }
}



/*====================
18. Hero css 
======================*/

.hero-section {
  position: relative;
}

.hero-scroll-box {
  position: absolute;
  left: 55px;
  bottom: 50px;
  z-index: 2;
}

.hero-scroll {
  width: 30px;
  height: 60px;
  border: 3px solid var(--color-white);
  border-radius: 15px;
  position: relative;
}

.hero-scroll .scroller {
  width: 16px;
  border-radius: 8px;
  background-color: var(--color-white);
  position: absolute;
  top: 4px;
  left: 4px;
  bottom: 34px;
  animation: scroller 1500ms ease-out infinite;
}

@keyframes scroller {
  0% {
    bottom: 34px;
  }

  5% {
    top: 4px;
  }

  32% {
    bottom: 4px;
  }

  66% {
    top: 34px;
    bottom: 4px;
  }

  100% {
    top: 4px;
    bottom: 34px;
  }
}

.hero-single {
  padding-top: 150px;
  padding-bottom: 160px;
  background-position: center !important;
  background-size: cover !important;
  background-repeat: no-repeat !important;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.hero-single::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  left: -0.5px;
  top: 0;
  background: var(--hero-overlay);
  opacity: 0.7;
  z-index: -1;
}

.hero-single .hero-content {
  height: 100%;
}

.hero-date {
  display: flex;
  align-items: center;
  gap: 15px;
}

.hero-date h1 {
  color: var(--theme-color);
  font-size: 90px;
  font-weight: 700;
}

.hero-date .date-content span {
  color: var(--color-white);
  text-transform: uppercase;
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 1px;
}

.hero-date .date-content p {
  color: var(--color-white);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.hero-single .hero-content .hero-title {
  color: var(--color-white);
  font-size: 60px;
  font-weight: 700;
  margin: 20px 0;
  text-transform: uppercase;
}

.hero-single .hero-content .hero-title span {
  color: var(--theme-color);
  font-weight: 500;
}

.hero-single .hero-content .hero-sub-title {
  display: inline-block;
  color: var(--theme-color);
  font-size: 25px;
  letter-spacing: 6px;
  font-weight: 600;
  position: relative;
  text-transform: uppercase;
}

.hero-single .hero-content p {
  color: var(--color-white);
  font-size: 20px;
  line-height: 30px;
  font-weight: 400;
  margin-bottom: 20px;
}

.hero-single .hero-content .hero-btn {
  gap: 1rem;
  display: flex;
  margin-top: 35px;
  justify-content: start;
}

.hero-slider.owl-theme .owl-nav {
  margin-top: 0px;
}

.hero-slider.owl-theme .owl-nav [class*=owl-] {
  color: var(--color-white);
  font-size: 25px;
  margin: 0;
  padding: 0;
  background: var(--slider-arrow-bg);
  display: inline-block;
  cursor: pointer;
  height: 55px;
  width: 55px;
  line-height: 55px;
  border-radius: 50px;
  text-align: center;
  transition: var(--transition);
}

.hero-slider.owl-theme .owl-nav [class*=owl-]:hover {
  background: var(--color-white);
  color: var(--theme-color);
}

.hero-slider.owl-theme .owl-nav .owl-prev {
  left: 40px;
}

.hero-slider.owl-theme .owl-nav .owl-next {
  right: 40px;
}

.hero-slider.owl-theme .owl-nav .owl-prev,
.hero-slider.owl-theme .owl-nav .owl-next {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
}

.hero-slider.owl-theme .owl-dots {
  position: absolute;
  text-align: center;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
}

.hero-slider.owl-theme .owl-dots .owl-dot span {
  background: var(--color-white);
  margin: 5px;
  border-radius: 10px;
  width: 15px;
  height: 15px;
  display: inline-block;
  transition: var(--transition);
}

.hero-slider.owl-theme .owl-dots .owl-dot.active span {
  background-color: var(--theme-color);
}

@media all and (max-width: 1199px) {
  .hero-single .hero-content .hero-title {
    font-size: 37px;
  }

  .hero-slider.owl-theme .owl-nav .owl-prev,
  .hero-slider.owl-theme .owl-nav .owl-next {
    top: unset;
    bottom: 70px !important;
  }

  .hero-slider.owl-theme .owl-nav .owl-prev {
    left: unset;
    right: 120px;
  }

  .hero-slider.owl-theme .owl-nav .owl-next {
    right: 40px;
  }
}

@media all and (max-width: 991px) {
  .hero-single .hero-content .hero-title {
    font-size: 45px;
  }

  .hero-scroll-box {
    left: 20px;
    bottom: 80px;
  }
}

@media all and (max-width: 767px) {
  .hero-single .hero-content .hero-sub-title {
    font-size: 18px;
  }

  .hero-single .hero-content .hero-btn {
    gap: 1rem;
  }
}



/*===================
19. Play btn
=====================*/

.play-btn {
  display: inline-block;
  padding: 0;
  height: 75px;
  width: 75px;
  line-height: 75px;
  font-size: 20px;
  text-align: center;
  background: var(--theme-color);
  color: var(--color-white) !important;
  position: absolute;
  border-radius: 50%;
  z-index: 1;
}

.play-btn i::after {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: -1;
  background-color: var(--theme-color);
  border-radius: 50px;
  animation: ripple-wave 1s linear infinite;
  -webkit-transform: scale(1);
  transform: scale(1);
  transition: all 0.5s ease-in-out;
}

@keyframes ripple-wave {
  0% {
    opacity: 0.8;
    -webkit-transform: scale(0.9);
    transform: scale(0.9);
  }

  100% {
    opacity: 0;
    -webkit-transform: scale(2);
    transform: scale(2);
  }
}



/*======================
20. Event countdown css
========================*/

.event-countdown {
  position: relative;
}

.event-countdown .time-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 18px;
}

.event-countdown .time {
  border-radius: 15px;
  padding: 14px 10px;
  text-align: center;
}

.event-countdown .time span {
  display: block;
}

.event-countdown .time span:first-child {
  color: var(--color-white);
  font-weight: 700;
  font-size: 38px;
  line-height: 1;
}

.event-countdown .time .unit {
  color: var(--color-white);
  font-weight: 500;
}

.event-countdown .divider {
  display: none;
}

/* event-countdown 1 */
.event-countdown.ec-1 {
  background: var(--theme-gradient);
  width: fit-content;
  margin-left: auto;
  margin-top: -135px;
  position: relative;
  padding: 20px 70px 20px 20px;
  z-index: 1;
}

.event-countdown.ec-1 .event-countdown-text {
  position: absolute;
  right: 0;
  top: 0;
  background: var(--color-white);
  width: 50px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.event-countdown.ec-1 .event-countdown-text span {
  writing-mode: vertical-lr;
  color: var(--theme-color2);
  text-transform: uppercase;
  font-weight: bold;
  letter-spacing: 1px;
}

/* event-countdown 2 */
.event-countdown.ec-2 {
  background: var(--theme-bg-light);
}

.event-countdown.ec-2 .time-wrap .time span:first-child {
  font-size: 75px;
  color: var(--theme-color2);
}

.event-countdown.ec-2 .time-wrap .time .unit {
  color: var(--theme-color);
  font-size: 25px;
}

.event-countdown.ec-2 .time-wrap .divider {
  display: block;
  color: var(--color-dark);
  font-size: 70px;
}


@media all and (max-width: 991px) {
  .event-countdown.ec-1 {
    margin-top: 0px;
  }
}

@media all and (max-width: 767px) {
  .event-countdown .time span:first-child {
    font-size: 22px;
  }

  .event-countdown.ec-1 {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .event-countdown .time-wrap {
    gap: 10px;
  }
}



/* ===================
21. About css 
====================== */

.about-area {
  position: relative;
  display: block;
}

.about-left {
  position: relative;
  display: block;
  margin-right: 50px;
}

.about-img {
  position: relative;
}

.about-img img {
  border-radius: 50%;
  border: 10px solid var(--color-white);
  box-shadow: var(--box-shadow);
}

.about-img .img-2 {
  position: absolute;
  right: -50px;
  bottom: -50px;
  width: 280px;
  box-shadow: none;
}

.about-img .img-3 {
  position: absolute;
  right: -30px;
  top: -30px;
  width: 200px;
  box-shadow: none;
}

.about-right {
  position: relative;
  display: block;
  padding-left: 30px;
}

.about-experience {
  background: var(--theme-gradient);
  box-shadow: var(--box-shadow);
  position: absolute;
  left: -30px;
  bottom: 80px;
  padding: 40px 20px;
  border-radius: 10px;
  text-align: center;
}

.about-experience h5 {
  color: var(--color-white);
  font-size: 20px;
}

.about-experience span {
  color: var(--color-white);
  font-size: 70px;
  font-weight: 900;
  line-height: 1;
  margin-bottom: 10px;
  display: inline-block;
}

.about-list-wrap {
  position: relative;
  display: block;
  margin-top: 25px;
  margin-bottom: 35px;
}

.about-list {
  position: relative;
  display: block;
}

.about-list li {
  position: relative;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--border-info-color);
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.about-list li:last-child {
  border-bottom: none;
}

.about-list li .icon {
  position: relative;
  display: flex;
  align-items: center;
}

.about-list li .icon i {
  font-size: 50px;
  color: var(--theme-color);
}

.about-list li .icon img {
  width: 50px;
}

.about-list li .about-item h4 span {
  color: var(--theme-color);
  margin-right: 10px;
}

.about-list li .about-item p {
  margin-top: 5px;
}

@media all and (max-width: 991px) {
  .about-right {
    margin-top: 90px;
  }
}

@media all and (max-width: 767px) {
  .about-right {
    padding-left: 0;
  }

  .about-title {
    font-size: 30px;
  }

  .about-left {
    margin-right: 0;
  }

  .about-left-content {
    bottom: -70px;
  }

  .about-img {
    width: 90%;
  }

  .about-experience {
    left: -7px;
    padding: 20px 10px;
  }

  .about-img .img-3 {
    width: 140px;
  }

  .about-img .img-2 {
    width: 150px;
    right: -10px;
  }
}



/*====================
22. Feature css 
======================*/

.feature-area {
  position: relative;
}

.feature-area.fa-negative {
  margin-top: -120px;
}

.feature-item {
  padding: 25px 25px;
  text-align: center;
  position: relative;
  background: var(--color-white);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  z-index: 1;
}

.feature-item:hover {
  transform: translateY(-10px);
}

.feature-item .count {
  font-size: 120px;
  font-weight: 800;
  -webkit-text-stroke: 2px var(--theme-color);
  -webkit-text-fill-color: transparent;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  opacity: .15;
  z-index: -1;
}

.feature-icon {
  margin-bottom: 20px;
  transition: var(--transition);
}

.feature-icon img {
  width: 80px;
}

.feature-item:hover .feature-icon {
  transform: rotateY(360deg);
}

.feature-item h4 {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 8px;
}



/* ===================
23. Schedule css 
====================== */

.schedule-area {
  position: relative;
}

.schedule-nav {
  border-bottom: 1px solid var(--border-info-color);
  padding-bottom: 35px;
  margin-bottom: 35px;
}

.schedule-nav .nav {
  background: var(--color-white);
  border-radius: 15px;
  width: fit-content;
  box-shadow: var(--box-shadow);
  margin: 0 auto;
  padding: 10px;
  gap: 10px;
}

.schedule-nav .nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 5px 15px;
  color: var(--color-dark);
  border-radius: 10px;
}

.schedule-nav .nav-pills .nav-link.active {
  background: var(--theme-gradient);
}

.schedule-nav .nav-link .icon {
  font-size: 30px;
  color: var(--theme-color);
}

.schedule-nav .nav-pills .nav-link.active .icon {
  color: var(--color-white);
}

.schedule-nav .nav-link .content span {
  display: block;
  text-align: left;
  line-height: 1;
}

.schedule-nav .nav-link .content .day {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 5px;
}

.schedule-nav .nav-link .content .date {
  font-weight: 500;
}

.schedule-item {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
}

.schedule-item::before {
  content: "";
  position: absolute;
  border-left: 3px dashed var(--theme-color2);
  left: 35px;
  top: 50%;
  height: 100%;
  z-index: -1;
}

.schedule-item.last::before {
  display: none;
}

.schedule-count {
  width: 70px;
  height: 70px;
  line-height: 68px;
  background: var(--theme-gradient);
  border-radius: 50%;
  text-align: center;
  font-size: 38px;
  font-weight: 700;
  color: var(--color-white);
  box-shadow: var(--box-shadow);
}

.schedule-content-wrap {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
  background: var(--color-white);
  padding: 15px;
  border-radius: 15px;
  box-shadow: var(--box-shadow);
}

.schedule-img {
  overflow: hidden;
  border-radius: 15px;
}

.schedule-img img {
  width: 280px;
  border-radius: 15px;
}

.schedule-item:hover .schedule-img img {
  transform: scale(1.1);
}

.schedule-content {
  flex: 1;
}

.schedule-meta {
  margin-bottom: 15px;
}

.schedule-meta ul {
  display: flex;
  gap: 20px;
}

.schedule-meta ul li {
  color: var(--theme-color);
  font-weight: 500;
}

.schedule-meta ul li i {
  color: var(--theme-color);
}

.schedule-info h4 {
  margin-bottom: 10px;
}

.schedule-info h4 a {
  color: var(--color-dark);
}

.schedule-info h4 a:hover {
  color: var(--theme-color);
}

.schedule-bottom {
  display: flex;
  justify-content: space-between;
  margin-top: 18px;
  padding-top: 18px;
  border-top: 1px solid var(--border-info-color);
}

.schedule-speaker {
  display: flex;
  gap: 20px;
}

.schedule-speaker .speaker-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.schedule-speaker .speaker-img {
  position: relative;
}

.schedule-speaker .speaker-img img {
  width: 50px;
  border-radius: 50px;
}

.schedule-speaker .speaker-img-icon {
  position: absolute;
  right: 0px;
  bottom: 0px;
  width: 22px;
  height: 22px;
  line-height: 20px;
  font-size: 14px;
  color: var(--color-white);
  background: var(--theme-color);
  border: 2px solid var(--color-white);
  border-radius: 50px;
  text-align: center;
}

/* schedule-area2 */
.schedule-area2 .schedule-nav .nav {
  border-radius: 50px;
}

.schedule-area2 .schedule-nav .nav-pills .nav-link {
  padding: 8px 30px;
}

.schedule-area2 .schedule-nav .nav-pills .nav-link.active {
  border-radius: 50px;
}

.schedule-area2 .schedule-item::before {
  display: none;
}

.schedule-area2 .schedule-count {
  color: var(--color-dark);
  background: transparent;
  width: unset;
  height: unset;
  line-height: unset;
  box-shadow: none;
  line-height: 1;
  font-weight: 800;
  color: var(--theme-color);
}

.schedule-area2 .schedule-count span {
  display: block;
  line-height: 1;
  font-weight: 500;
  font-size: 20px;
  text-transform: uppercase;
  margin-top: 5px;
  color: var(--color-dark);
}

.schedule-area2 .schedule-bottom {
  border-top: none;
  margin: 0;
  padding: 0;
}

.schedule-area2 .schedule-img img {
  width: 180px;
}

.schedule-area2 .schedule-content {
  border-right: 1px solid var(--border-info-color);
  padding-right: 20px;
}

/* schedule-area3 */
.schedule-area3 .schedule-nav .nav {
  background: transparent;
  box-shadow: none;
  padding: 0;
  gap: 20px;
}

.schedule-area3 .schedule-nav .nav-pills .nav-link {
  background: var(--color-white);
  box-shadow: var(--box-shadow);
}

.schedule-area3 .schedule-nav .nav-pills .nav-link.active {
  background: var(--theme-gradient);
}

.schedule-area3 .schedule-item::before {
  display: none;
}

.schedule-area3 .schedule-content-wrap {
  flex-direction: column;
  gap: 15px;
}

.schedule-area3 .schedule-img img {
  width: 100%;
}

.schedule-area3 .schedule-bottom {
  width: 100%;
  margin-top: 0px;
}

.schedule-item .time {
  color: var(--theme-color2);
  font-weight: 500;
}

.schedule-area3 .schedule-info .location {
  color: var(--theme-color);
  font-weight: 500;
}

.schedule-area3 .schedule-info h4 {
  margin-top: 10px;
}


@media all and (max-width: 991px) {
  .schedule-item::before {
    display: none;
  }

  .schedule-count {
    display: none;
  }

  .schedule-content-wrap {
    flex-direction: column;
    align-items: unset;
  }

  .schedule-img img {
    width: 100%;
  }

  .schedule-meta ul {
    flex-direction: column;
    gap: 10px;
  }

  .schedule-speaker,
  .schedule-bottom {
    flex-direction: column;
  }

  .schedule-bottom .theme-btn {
    margin-top: 20px;
  }

  .schedule-area2 .schedule-img img {
    width: 100%;
  }

  .schedule-area2 .schedule-content {
    border-right: none;
    padding-right: 0px;
  }
}



/* ===================
24. Schedule single css 
====================== */

.schedule-single {
  position: relative;
}

.schedule-detail-img img {
  border-radius: 15px;
}

.schedule-sidebar .event-countdown {
  background: var(--theme-gradient);
  border-radius: 15px;
}

.schedule-sidebar .event-countdown .time-wrap {
  gap: 0;
}

.schedule-sidebar .schedule-list li {
  color: var(--color-dark);
  font-weight: 500;
  margin: 5px 0;
}

.schedule-sidebar .schedule-list li i {
  color: var(--theme-color);
  width: 25px;
}

.schedule-sidebar .social {
  margin-top: 30px;
}

.schedule-sidebar .social a {
  width: 35px;
  height: 35px;
  line-height: 32px;
  border: 2px solid var(--theme-color2);
  color: var(--theme-color2);
  text-align: center;
  border-radius: 50px;
  margin-right: 5px;
}

.schedule-sidebar .social a:hover {
  background: var(--theme-color2);
  color: var(--color-white);
}

.schedule-info-list li {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 10px 0;
}

.schedule-info-list .icon {
  width: 35px;
  height: 35px;
  line-height: 35px;
  background: var(--theme-color);
  color: var(--color-white);
  text-align: center;
  border-radius: 50px;
  margin-right: 5px;
}

.schedule-info-list .theme-btn {
  margin-top: 20px;
}

.schedule-info-list .content span {
  color: var(--theme-color);
  font-weight: 500;
}

.schedule-detail-info .video-content,
.schedule-detail-info .video-content::before {
  border-radius: 15px;
}

.schedule-detail-info .location-map iframe {
  border-radius: 15px;
  width: 100%;
  height: 250px;
}




/* ===================
25. Pricing css 
====================== */

.pricing-item {
  position: relative;
  background: var(--color-white);
  padding: 10px 10px 30px 10px;
  border-radius: 20px;
  overflow: hidden;
  transition: var(--transition);
  z-index: 1;
}

.pricing-shape img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: -1;
}

.pricing-item:hover {
  transform: translateY(-10px);
}

.pricing-header-wrap {
  margin-bottom: 30px;
}

.pricing-header {
  text-align: center;
  margin-top: 15px;
}

.pricing-header h5 {
  font-size: 25px;
  font-weight: 600;
  display: inline-block;
  color: var(--color-white);
  margin-bottom: 10px;
}

.pricing-amount {
  text-align: center;
  margin-top: 105px;
  margin-bottom: 25px;
}

.pricing-amount strong {
  font-size: 50px;
  font-weight: 900;
  color: var(--color-dark);
  line-height: 1;
}

.pricing-feature {
  position: relative;
  padding: 35px 20px 30px 20px;
  border-top: 2px dashed var(--theme-color);
}

.pricing-feature::before {
  content: "";
  position: absolute;
  left: -20px;
  top: -14px;
  width: 25px;
  height: 25px;
  border-radius: 50px;
  background: var(--theme-bg-light);
}

.pricing-feature::after {
  content: "";
  position: absolute;
  right: -20px;
  top: -14px;
  width: 25px;
  height: 25px;
  border-radius: 50px;
  background: var(--theme-bg-light);
}

.pricing-feature li {
  margin-bottom: 15px;
}

.pricing-feature li:last-child {
  margin-bottom: 0px;
}

.pricing-feature li i {
  color: var(--theme-color);
  margin-right: 10px;
}

.pricing-btn-wrap {
  text-align: center;
}

/* pricing-area2 */
.pricing-area2 .pricing-item {
  background: var(--color-white);
  box-shadow: var(--box-shadow);
}

.pricing-area2 .pricing-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: -80px;
  width: 100%;
  height: 170px;
  background: var(--theme-gradient);
  border-radius: 20px 20px 20px 80px;
  transform: rotate(-20deg);
  z-index: -1;
}

.pricing-area2 .pricing-item::after {
  content: "";
  position: absolute;
  right: -30px;
  top: 70px;
  width: 140px;
  height: 20px;
  background: var(--theme-gradient);
  border-radius: 20px;
  transform: rotate(-20deg);
}


@media all and (max-width: 991px) {
  .pricing-amount strong {
    font-size: 50px;
  }
}




/*====================
26. Video css 
======================*/

.video-content {
  position: relative;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.video-content::before {
  content: "";
  position: absolute;
  background: rgba(11, 9, 23, .5);
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}

.video-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  border-radius: 5px;
  height: 500px;
  z-index: 1;
}

.video-wrapper img {
  border-radius: 12px;
}

.video-area .play-btn {
  display: inline-block;
  padding: 0;
  height: 75px;
  width: 75px;
  text-align: center;
  position: absolute;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@media all and (max-width: 991px) {
  .video-content {
    padding-top: 80px;
  }

  .video-wrapper {
    height: 250px;
  }
}



/* ===================
27. Counter css 
====================== */

.counter-area {
  position: relative;
  background-image: url(../img/shape/02.png);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}

.counter-info {
  padding-right: 20px;
}

.counter-box {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 20px;
  position: relative;
  background: var(--color-white);
  padding: 30px 25px 30px 25px;
  border-radius: 15px;
  box-shadow: 0 0 40px 5px rgb(0 0 0 / 5%);
  z-index: 1;
}

.counter-box .icon {
  width: 70px;
  height: 70px;
  line-height: 60px;
  font-size: 35px;
  border-radius: 50px;
  text-align: center;
  color: var(--color-white);
  background: var(--theme-gradient);
  position: relative;
}

.counter-box .icon::before {
  content: "";
  position: absolute;
  left: -7px;
  top: -7px;
  bottom: -7px;
  right: -7px;
  border: 2px dashed var(--theme-color);
  border-radius: 50%;
  transition: all .5s ease-in-out;
  z-index: -1;
}

.counter-box .icon img {
  width: 42px;
  filter: brightness(0) invert(1);
}

.counter-box .counter-info {
  display: flex;
  align-items: center;
  gap: 2px;
}

.counter-box .counter-unit {
  color: var(--color-dark);
  font-weight: 600;
  font-size: 25px;
}

.counter-box .counter {
  display: block;
  line-height: 1;
  color: var(--color-dark);
  font-size: 50px;
  font-weight: 900;
}

.counter-box .title {
  color: var(--color-dark);
  margin-top: 5px;
  font-size: 18px;
  font-weight: 600;
  text-transform: capitalize;
}



/* ===================
28. Team css 
====================== */

.team-area {
  position: relative;
  overflow: hidden;
}

.team-item {
  position: relative;
  text-align: center;
  transition: var(--transition);
}

.team-item:hover {
  transform: translateY(-8px);
}

.team-img {
  padding: 6px;
  border: 3px solid var(--theme-color);
  border-radius: 50%;
}

.team-img img {
  border-radius: 50%;
  width: 100%;
}

.team-content .social {
  position: relative;
  background: var(--theme-color);
  width: fit-content;
  padding: 0 8px;
  margin: -20px auto 0 auto;
  border-radius: 50px;
  z-index: 1;
}

.team-content .social a {
  width: 25px;
  color: var(--color-white);
}

.team-content .social a:hover {
  color: var(--color-dark);
}

.team-content .info {
  margin-top: 15px;
}

.team-content .info h4 {
  font-size: 20px;
  color: var(--color-dark);
}

.team-content .info span {
  color: var(--theme-color);
  font-weight: 500;
}

/* team-area 2 */
.team-area2 .team-item {
  border-radius: 15px;
  box-shadow: var(--box-shadow);
  padding-bottom: 12px;
}

.team-area2 .team-img {
  border: none;
}

.team-area2 .team-img img {
  border-radius: 15px;
}

.team-area2 .team-content .social {
  background: var(--color-white);
}

.team-area2 .team-content .social a {
  color: var(--theme-color);
}

.team-area2 .team-content .social a:hover {
  color: var(--theme-color2);
}




/*====================
29. Team single css 
======================*/

.team-single {
  position: relative;
}

.team-single-content {
  background: var(--color-white);
  box-shadow: var(--box-shadow);
  border-radius: 15px;
  padding: 20px;
}

.team-single-img img {
  border-radius: 15px;
}

.team-details .name {
  color: var(--color-dark);
  margin-bottom: 5px;
}

.team-details .designation {
  color: var(--theme-color);
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 3px;
}

.team-details-info {
  margin-top: 10px;
  margin-bottom: 20px;
}

.team-details-info ul {
  margin-top: 15px;
}

.team-details-info ul li {
  margin: 10px 0;
  color: var(--color-dark);
}

.team-details-info ul li a span {
  width: 110px;
  display: inline-block;
  font-weight: 500;
  color: var(--color-dark);
}

.team-details-info ul li i {
  color: var(--theme-color);
  width: 25px;
}

.team-details-social a {
  width: 30px;
  color: var(--theme-color2);
}

.team-details-social a:hover {
  color: var(--theme-color);
}

/* team-session */
.team-session .session-item {
  background: var(--color-white);
  border-radius: 15px;
  padding: 20px;
  box-shadow: var(--box-shadow);
}

.team-session .session-item .day {
  color: var(--theme-color2);
  text-transform: uppercase;
  letter-spacing: 3px;
}

.team-session .session-meta {
  display: flex;
  gap: 15px;
  margin-top: 15px;
  margin-bottom: 15px;
}

.team-session .session-meta li {
  color: var(--body-text-color);
}

.team-session .session-meta li i {
  color: var(--theme-color);
  margin-right: 5px;
}

.team-session .session-content p {
  margin-top: 15px;
  color: var(--body-text-color);
}

.team-session .session-content p span {
  color: var(--color-dark);
  font-weight: 500;
}

.team-session .session-content p i {
  color: var(--theme-color);
  margin-right: 5px;
}

.team-session .session-content .theme-btn {
  margin-top: 20px;
}



/*====================
30. Cta css 
======================*/

.cta-area {
  position: relative;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  z-index: 1;
}

.cta-area::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: var(--theme-gradient);
  opacity: .85;
  z-index: -1;
}

.cta-content {
  text-align: center;
}

.cta-content h6 {
  font-size: 25px;
  color: var(--theme-color2);
  text-transform: uppercase;
  font-family: var(--heading-font2);
  letter-spacing: 3px;
}

.cta-content h1 {
  font-family: var(--heading-font2);
  color: var(--color-white);
  font-size: 50px;
  text-transform: capitalize;
  margin-bottom: 20px;
}

.cta-content p {
  color: var(--color-white);
  font-size: 18px;
  margin-bottom: 30px;
}

.cta-content .theme-btn {
  background: var(--theme-color);
  color: var(--color-white);
}



/*====================
31. Choose css 
======================*/

.choose-area {
  position: relative;
}

.choose-content-wrap {
  margin-top: 30px;
}

.choose-item {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.choose-item-icon {
  width: 60px;
  height: 60px;
  line-height: 55px;
  border-radius: 50px;
  background: var(--theme-color);
  text-align: center;
  box-shadow: var(--box-shadow);
}

.choose-item-icon img {
  width: 35px;
  filter: brightness(0) invert(1);
}

.choose-item-info h4 {
  margin-bottom: 5px;
}

.choose-item-info {
  border-bottom: 1px solid var(--border-info-color);
  padding-bottom: 20px;
  flex: 1;
}

.choose-item:last-child .choose-item-info {
  border-bottom: none;
}

.choose-img {
  position: relative;
  text-align: end;
  margin-right: 30px;
}

.choose-img img {
  border-radius: 20px;
}

.choose-img::before {
  content: "";
  position: absolute;
  left: 15px;
  top: 40px;
  background: var(--theme-color);
  border-radius: 20px;
  width: 70%;
  height: 88%;
  transform: rotate(-8deg);
  z-index: -1;
}

.choose-img .theme-btn {
  position: absolute;
  left: 0;
  bottom: 80px;
}

@media all and (max-width: 991px) {
  .choose-content {
    margin-top: 50px;
  }

  .choose-img {
    text-align: left;
    padding-left: 40px;
  }
}



/*====================
32. Venue css 
======================*/

.venue-area {
  position: relative;
}

.venue-item {
  position: relative;
}

.venue-img {
  width: 85%;
}

.venue-img img {
  border-radius: 15px;
}

.venue-content {
  position: absolute;
  right: 0;
  bottom: 30px;
  width: 230px;
  background: var(--color-white);
  border-radius: 15px;
  padding: 20px;
  box-shadow: var(--box-shadow);
  z-index: 1;
}

.venue-content span {
  color: var(--theme-color);
  text-transform: uppercase;
  letter-spacing: 3px;
  font-weight: 500;
  font-size: 14px;
}

.venue-content h6 {
  margin-top: 10px;
  margin-bottom: 5px;
}

.venue-content p {
  color: var(--body-text-color);
}

.venue-play {
  margin-top: 15px;
}

.venue-play .popup-youtube {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 15px;
}

.venue-play .popup-youtube i {
  width: 35px;
  height: 35px;
  line-height: 35px;
  background: var(--theme-gradient);
  border-radius: 50px;
  color: var(--color-white);
  text-align: center;
}



/*====================
33. Venue single css 
======================*/

.venue-single {
  position: relative;
}

.venue-list li {
  margin: 8px 0;
  display: flex;
  align-items: center;
}

.venue-list li span {
  font-weight: 500;
  color: var(--color-dark);
}

.venue-list li span:first-child {
  width: 140px;
  display: inline-block;
}

.venue-list li span:first-child span {
  font-weight: 400;
}

.venue-capacity .venue-list li span:nth-child(2) {
  width: 50px;
}

.venue-info .venue-list li span:first-child {
  width: 80px;
}

.venue-info .venue-list li span:nth-child(2) {
  width: 20px;
}

.venue-info .venue-list li span:last-child {
  flex: 1;
}

.venue-detail-img img {
  border-radius: 15px;
}

.venue-detail-info .video-content,
.venue-detail-info .video-content::before {
  border-radius: 20px;
}

.venue-single-list li {
  margin: 8px 0;
}

.venue-single-list li i {
  color: var(--theme-color);
  margin-right: 10px;
}




/*====================
34. Testimonial css 
======================*/

.testimonial-area {
  position: relative;
  overflow: hidden;
}

.testimonial-area .site-shadow-text {
  color: var(--color-dark);
  font-size: 180px;
  right: unset;
  left: 50%;
  transform: translateX(-50%);
  opacity: .05;
}

.testimonial-single {
  margin-bottom: 20px;
}

.testimonial-quote {
  background: var(--theme-color);
  padding: 30px 30px 40px 30px;
  position: relative;
  border-radius: 20px;
}

.testimonial-quote::before {
  content: "\f0dd";
  position: absolute;
  font-family: "Font Awesome 5 Pro";
  bottom: -52px;
  left: 40px;
  font-weight: bold;
  font-size: 70px;
  color: var(--theme-color);
}

.testimonial-quote p {
  color: var(--color-white);
  font-size: 18px;
  font-style: italic;
  font-weight: 500;
}

.testimonial-content {
  display: flex;
  justify-content: start;
  align-items: center;
  margin-top: 30px;
  margin-left: 20px;
}

.testimonial-author-img {
  margin-right: 20px;
  width: 80px;
  padding: 5px;
  border-radius: 50px;
  border: 2px dashed var(--theme-color);
}

.testimonial-author-img img {
  border-radius: 50%;
}

.testimonial-author-info h4 {
  font-size: 20px;
  color: var(--color-dark);
}

.testimonial-author-info p {
  color: var(--theme-color);
}

.testimonial-quote-icon {
  position: absolute;
  right: 20px;
  top: 0px;
  opacity: .08;
}

.testimonial-quote-icon i {
  font-size: 55px;
  color: var(--theme-color);
}

.testimonial-quote-icon img {
  width: 150px !important;
}

.testimonial-rate {
  color: var(--theme-color2);
  width: fit-content;
  margin-bottom: 10px;
  background: var(--color-white);
  padding: 0 10px;
  border-radius: 50px;
}

.testimonial-area .owl-dots {
  text-align: center;
  margin-top: 30px;
}

.testimonial-area .owl-dots .owl-dot span {
  background: var(--theme-color);
  margin: 5px;
  border-radius: 10px;
  width: 15px;
  height: 7px;
  display: inline-block;
  transition: var(--transition);
}

.testimonial-area .owl-dots .owl-dot.active span {
  background-color: var(--theme-color);
  width: 8px;
  height: 8px;
}

/*====================
34.1. Video Testimonial css
======================*/

.video-testimonial-area {
  position: relative;
  overflow: hidden;
}

.video-testimonial-area .site-shadow-text {
  color: var(--color-dark);
  font-size: 180px;
  right: unset;
  left: 50%;
  transform: translateX(-50%);
  opacity: .05;
}

.video-testimonial .testimonial-video-wrapper {
  background: var(--theme-color);
  padding: 20px;
  border-radius: 20px;
  position: relative;
  margin-bottom: 20px;
}

.video-testimonial .video-thumbnail {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  cursor: pointer;
}

.video-testimonial .video-thumbnail img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: var(--transition);
}

.video-testimonial .video-thumbnail:hover img {
  transform: scale(1.05);
}

.video-testimonial .video-play-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70px;
  height: 70px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.video-testimonial .video-play-btn:hover {
  background: var(--color-white);
  transform: translate(-50%, -50%) scale(1.1);
}

.video-testimonial .video-play-btn i {
  font-size: 24px;
  color: var(--theme-color);
  margin-left: 3px;
}

.video-testimonial .testimonial-content {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  margin-left: 0;
}

.video-testimonial .testimonial-rate {
  color: var(--theme-color2);
  width: fit-content;
  margin-top: 5px;
  background: var(--color-white);
  padding: 0 10px;
  border-radius: 50px;
}

/* Video Modal Styles */
.video-modal {
  position: fixed;
  z-index: 9999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-modal-content {
  position: relative;
  width: 90%;
  max-width: 800px;
  background: var(--color-white);
  border-radius: 10px;
  overflow: hidden;
}

.video-modal-content iframe {
  width: 100%;
  height: 450px;
}

.video-close {
  position: absolute;
  top: -40px;
  right: 0;
  color: var(--color-white);
  font-size: 30px;
  font-weight: bold;
  cursor: pointer;
  z-index: 10000;
}

.video-close:hover {
  color: var(--theme-color);
}

.video-testimonial-area .owl-dots {
  text-align: center;
  margin-top: 30px;
}

.video-testimonial-area .owl-dots .owl-dot span {
  background: var(--theme-color);
  margin: 5px;
  border-radius: 10px;
  width: 15px;
  height: 7px;
  display: inline-block;
  transition: var(--transition);
}

.video-testimonial-area .owl-dots .owl-dot.active span {
  background-color: var(--theme-color);
  width: 8px;
  height: 8px;
}

/*====================
35. Quote css
======================*/

.quote-area {
  position: relative;
  background-image: url(../img/quote/01.jpg);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}

.quote-content {
  background: var(--color-white);
  padding: 35px;
  border-radius: 20px;
  margin-top: -100px;
  box-shadow: var(--box-shadow);
}

.quote-img img {
  border-radius: 15px;
}

.quote-head {
  margin-bottom: 20px;
}

.quote-head h3 {
  margin-bottom: 8px;
}

.quote-form .input-group {
  margin-bottom: 22px;
}

.quote-form .input-group:focus-within {
  outline: 2px solid var(--theme-color);
  border-radius: 50px;
}

.quote-form .input-group.textarea:focus-within {
  border-radius: 30px;
}

.quote-form .input-group .form-control {
  padding: 15px 25px 15px 0px;
  border-radius: 50px;
  border: none;
  box-shadow: none;
  background-color: var(--theme-bg-light);
}

.quote-form .input-group .form-control,
.quote-form .input-group .form-select,
.quote-form .input-group .form-control::placeholder {
  color: var(--color-dark);
}

.quote-form .input-group .input-group-text {
  background: var(--theme-bg-light);
  color: var(--theme-color);
  border-radius: 50px;
  padding-left: 20px;
  border: none;
}

.quote-form .input-group.textarea .input-group-text,
.quote-form .input-group.textarea .form-control {
  border-radius: 30px;
}

.quote-form .input-group.textarea .input-group-text {
  align-items: flex-start;
  padding-top: 20px;
}

/* quote-area2 */
.quote-area2 {
  position: relative;
}

.quote-area2 .quote-form {
  background: var(--color-white);
  padding: 30px;
  border-radius: 0 15px 15px 0;
  box-shadow: var(--box-shadow);
  margin-top: -50px;
  position: relative;
  z-index: 1;
}

.quote-area2 .quote-form .input-group {
  margin-bottom: 0;
}




/*====================
36. Instagram css 
======================*/

.instagram-area {
  position: relative;
}

.instagram-img {
  overflow: hidden;
  border-radius: 15px;
}

.instagram-img img {
  border-radius: 15px;
}

.instagram-img:hover img {
  transform: scale(1.1) rotate(2deg);
}

.instagram-area .theme-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}




/*====================
37. Blog css 
======================*/

.blog-area {
  position: relative;
}

.blog-item {
  padding: 20px;
  border: 2px solid rgba(140, 82, 255, .2);
  border-radius: 15px;
  transition: var(--transition);
  position: relative;
}

.blog-item:hover {
  border-color: rgba(140, 82, 255, 1);
}

.blog-date {
  display: block;
  position: absolute;
  right: 12px;
  top: 12px;
  color: var(--color-white);
  padding: 0px 10px;
  box-shadow: var(--box-shadow);
  z-index: 1;
}

.blog-date::before {
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: var(--theme-color);
  border-radius: 8px;
  transform: skewX(-10deg);
  z-index: -1;
}

.blog-item-img {
  border-radius: 15px;
  overflow: hidden;
}

.blog-item-img img {
  border-radius: 12px;
}

.blog-item:hover .blog-item-img img {
  transform: scale(1.1);
}

.blog-item-info {
  padding: 15px 0 0 0;
}

.blog-item-meta ul {
  margin: 0;
  margin-bottom: 14px;
  padding: 0;
  padding-bottom: 14px;
  border-bottom: 1px solid var(--border-info-color);
}

.blog-item-meta ul li {
  display: inline-block;
  margin-right: 15px;
  font-weight: 500;
  position: relative;
  color: var(--color-dark);
}

.blog-item-meta ul li i {
  margin-right: 5px;
  color: var(--theme-color);
}

.blog-item-meta a {
  color: var(--color-dark);
}

.blog-item-meta a:hover {
  color: var(--theme-color);
}

.blog-title {
  font-size: 22px;
  margin-bottom: 15px;
  text-transform: capitalize;
}

.blog-item-info h4 a {
  color: var(--color-dark);
}

.blog-item-info h4 a:hover {
  color: var(--theme-color);
}

.blog-item-info p {
  margin-bottom: 16px;
}

.blog-item-info .theme-btn {
  margin-top: 10px;
}




/*========================
38. Blog single css
==========================*/

.blog-single {
  position: relative;
}

.blog-single .blog-thumb-img {
  margin-bottom: 20px;
}

.blog-single .blog-single-content img {
  border-radius: 15px;
}

.blog-single .blog-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.blog-single .blog-meta-left ul {
  display: flex;
  align-items: center;
  gap: 20px;
}

.blog-single .blog-meta-left ul li {
  color: var(--color-dark);
  font-weight: 500;
}

.blog-single .blog-meta i {
  margin-right: 5px;
  color: var(--theme-color);
}

.blog-single .blog-meta a {
  color: var(--color-dark);
  font-weight: 500;
}

.blog-single .blog-meta a:hover {
  color: var(--theme-color);
}

.blog-single .blog-details-title {
  font-size: 34px;
  color: var(--color-dark);
}

.blog-single .blockqoute {
  background: var(--theme-bg-light);
  border-left: 5px solid var(--theme-color);
  padding: 30px;
  font-size: 17px;
  font-style: italic;
  margin: 20px 0;
  border-radius: 0px;
  position: relative;
}

.blog-single .blockqoute .blockqoute-icon {
  position: absolute;
  right: 20px;
  bottom: 5px;
  color: var(--theme-color);
  font-size: 70px;
}

.blog-single .blockqoute-author {
  margin-top: 20px;
  padding-left: 60px;
  position: relative;
  color: var(--color-dark);
}

.blog-single .blockqoute-author::before {
  content: "";
  position: absolute;
  height: 2px;
  width: 40px;
  background: var(--theme-color);
  left: 0;
  top: 10px;
}

.blog-single .blog-details-tags {
  display: flex;
  align-items: center;
  gap: 20px;
}

.blog-single .blog-details-tags h5 {
  color: var(--color-dark);
}

.blog-single .blog-details-tags ul {
  display: flex;
  align-items: center;
  gap: 15px;
}

.blog-single .blog-details-tags ul a {
  background: var(--theme-bg-light);
  color: var(--color-dark);
  padding: 5px 18px;
  border-radius: 50px;
  transition: var(--transition);
}

.blog-single .blog-details-tags ul a:hover {
  background: var(--theme-color);
  color: var(--color-white);
}

.blog-single .blog-author {
  display: flex;
  justify-content: start;
  align-items: center;
  background: var(--theme-bg-light);
  margin: 50px 0;
  padding: 20px;
  border-radius: 15px;
}

.blog-single .blog-author-img {
  width: 320px;
}

.blog-single .blog-author-img img {
  border-radius: 15px;
}

.blog-single .author-name {
  font-size: 22px;
  color: var(--theme-color);
  margin: 8px 0;
}

.blog-single .author-info {
  padding: 0 20px;
}

.blog-single .author-social {
  margin-top: 10px;
}

.blog-single .author-social a {
  width: 35px;
  height: 35px;
  line-height: 32px;
  text-align: center;
  border: 2px solid var(--theme-color);
  border-radius: 50px;
  margin-right: 5px;
  color: var(--theme-color);
  transition: var(--transition);
}

.blog-single .author-social a:hover {
  color: var(--color-white);
  background: var(--theme-color);
}

.blog-single .blog-comments h3 {
  color: var(--color-dark);
}

.blog-single .blog-comments-wrapper {
  margin: 30px 0;
}

.blog-single .blog-comments-single {
  display: flex;
  justify-content: start;
  align-items: flex-start;
  margin-top: 50px;
}

.blog-single .blog-comments-single img {
  border-radius: 50%;
}

.blog-single .blog-comments-content {
  padding: 0 0 0 20px;
}

.blog-single .blog-comments-content span {
  font-size: 14px;
  color: var(--theme-color);
  font-weight: 500;
}

.blog-single .blog-comments-content a {
  font-weight: 500;
  margin-top: 5px;
  color: var(--theme-color);
}

.blog-single .blog-comments-content a:hover {
  color: var(--theme-color2);
}

.blog-single .blog-comments-content h5 {
  color: var(--color-dark);
}

.blog-single .blog-comments-reply {
  margin-left: 50px;
}

.blog-single .blog-comments-form {
  padding: 30px;
  margin-top: 50px;
  border-radius: 15px;
  background: var(--theme-bg-light);
}

.blog-single .blog-comments-form h3 {
  margin-bottom: 20px;
}

.blog-single .blog-comments-form .input-group {
  margin-bottom: 22px;
}

.blog-single .blog-comments-form .input-group:focus-within {
  outline: 2px solid var(--theme-color);
  border-radius: 50px;
}

.blog-single .blog-comments-form .input-group.textarea:focus-within {
  border-radius: 30px;
}

.blog-single .blog-comments-form .input-group .form-control {
  padding: 15px 25px 15px 0px;
  border-radius: 50px;
  border: none;
  box-shadow: none;
  background: var(--color-white);
}

.blog-single .blog-comments-form .input-group .nice-select {
  line-height: 25px;
}

.blog-single .blog-comments-form .input-group .form-control,
.blog-single .blog-comments-form .input-group .form-select,
.blog-single .blog-comments-form .input-group .form-control::placeholder {
  color: var(--color-dark);
}

.blog-single .blog-comments-form .input-group .input-group-text {
  background: var(--color-white);
  color: var(--theme-color);
  border-radius: 50px;
  padding-left: 20px;
  border: none;
}

.blog-single .blog-comments-form .input-group.textarea .input-group-text,
.blog-single .blog-comments-form .input-group.textarea .form-control {
  border-radius: 30px;
}

.blog-single .blog-comments-form .input-group.textarea .input-group-text {
  align-items: flex-start;
  padding-top: 20px;
}


@media all and (max-width: 767px) {
  .blog-single .blog-meta {
    flex-direction: column;
    font-size: 15px;
  }

  .blog-single .blog-meta-left ul {
    gap: 10px;
  }

  .blog-single .blog-details-tags {
    flex-direction: column;
    align-items: flex-start;
  }

  .blog-single .blog-author {
    flex-direction: column;
    text-align: center;
    padding: 25px;
  }

  .blog-single .author-info {
    margin-top: 25px;
  }

  .blog-single .blog-comments-single {
    flex-direction: column;
    text-align: center;
    padding: 30px 0px;
    background: var(--color-white);
    box-shadow: var(--box-shadow);
    margin-bottom: 30px;
    border-radius: 15px;
  }

  .blog-single .blog-comments-single img {
    margin: 0 auto 20px auto;
  }

  .blog-single .blog-comments-reply {
    margin-left: 0px;
  }
}



/*=======================
39. Widget sidebar css
=========================*/

.widget {
  background: var(--theme-bg-light);
  padding: 30px;
  margin-bottom: 30px;
  border-radius: 15px;
}

.widget .widget-title {
  padding-bottom: 10px;
  margin-bottom: 30px;
  position: relative;
  font-size: 25px;
  color: var(--color-dark);
}

.widget .widget-title::before {
  position: absolute;
  content: '';
  width: 15px;
  border-bottom: 3px solid var(--theme-color);
  bottom: 0;
  left: 0;
}

.widget .widget-title::after {
  position: absolute;
  content: '';
  width: 30px;
  border-bottom: 3px solid var(--theme-color);
  bottom: 0;
  left: 22px;
}

.widget .search-form .form-control {
  padding: 12px 15px 12px 20px;
  border-radius: 50px;
  box-shadow: none;
  color: var(--color-dark);
}

.widget .search-form .form-control::placeholder {
  color: var(--color-dark);
}

.widget .search-form {
  position: relative;
}

.widget .search-form .form-control:focus {
  border-color: var(--theme-color);
}

.widget .search-form button {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 18px;
  padding: 8px 18px 6px 18px;
  background: transparent;
  border: none;
  color: var(--theme-color);
}

.widget .category-list a {
  display: block;
  padding: 10px 0;
  font-weight: 500;
  color: var(--color-dark);
  border-bottom: 1px solid var(--border-info-color);
  transition: var(--transition);
}

.widget .category-list a:last-child {
  margin-bottom: 0px;
  border-bottom: none;
}

.widget .category-list a:hover {
  padding-left: 10px;
  color: var(--theme-color);
}

.widget .category-list a i {
  margin-right: 5px;
  color: var(--theme-color);
}

.widget .category-list a span {
  float: right;
}

.widget .recent-post-single {
  display: flex;
  justify-content: start;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.widget .recent-post-img img {
  width: 75px;
  border-radius: 15px;
}

.widget .recent-post-bio {
  flex: 1;
}

.widget .recent-post-bio h6 {
  font-size: 18px;
}

.widget .recent-post-bio span {
  font-size: 14px;
  color: var(--theme-color);
  font-weight: 500;
}

.widget .recent-post-bio span i {
  margin-right: 5px;
}

.widget .recent-post-bio h6 a:hover {
  color: var(--theme-color);
}

.widget .social-share-link a {
  width: 35px;
  height: 35px;
  line-height: 32px;
  border: 2px solid var(--theme-color);
  color: var(--theme-color);
  text-align: center;
  margin-right: 5px;
  border-radius: 50px;
  transition: var(--transition);
}

.widget .social-share-link a:hover {
  background: var(--theme-color);
  color: var(--color-white);
}

.widget .tag-list a {
  background: var(--color-white);
  color: var(--color-dark);
  padding: 5px 18px;
  margin-bottom: 10px;
  margin-right: 10px;
  border-radius: 50px;
  display: inline-block;
  transition: var(--transition);
}

.widget .tag-list a:hover {
  background-color: var(--theme-color);
  color: var(--color-white);
}



/*===================
40. Contact us css 
=====================*/

.contact-wrapper {
  position: relative;
}

.contact-img img {
  width: 100%;
  border-radius: 15px;
}

.contact-form {
  background: var(--theme-bg-light);
  padding: 20px 25px;
  border-radius: 15px;
}

.contact-form-header {
  margin-bottom: 30px;
}

.contact-form-header h2 {
  font-size: 30px;
  font-weight: 700;
  margin-bottom: 10px;
  color: var(--color-dark);
}

.contact-form .input-group {
  margin-bottom: 22px;
}

.contact-form .input-group:focus-within {
  outline: 2px solid var(--theme-color);
  border-radius: 50px;
}

.contact-form .input-group.textarea:focus-within {
  border-radius: 30px;
}

.contact-form .input-group .form-control {
  padding: 15px 25px 15px 0px;
  border-radius: 50px;
  border: none;
  box-shadow: none;
  background: var(--color-white);
}

.contact-form .input-group .nice-select {
  line-height: 25px;
}

.contact-form .input-group .form-control,
.contact-form .input-group .form-select,
.contact-form .input-group .form-control::placeholder {
  color: var(--color-dark);
}

.contact-form .input-group .input-group-text {
  background: var(--color-white);
  color: var(--theme-color);
  border-radius: 50px;
  padding-left: 20px;
  border: none;
}

.contact-form .input-group.textarea .input-group-text,
.contact-form .input-group.textarea .form-control {
  border-radius: 30px;
}

.contact-form .input-group.textarea .input-group-text {
  align-items: flex-start;
  padding-top: 20px;
}

.contact-form .text-success {
  color: var(--theme-color);
}

.contact-map {
  margin-bottom: -9px;
}

.contact-map iframe {
  width: 100%;
  height: 450px;
}

.contact-content {
  margin-bottom: 50px;
}

.contact-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 15px;
  padding: 30px 20px;
  position: relative;
  margin-bottom: 25px;
  border-radius: 15px;
  background: var(--theme-bg-light);
  transition: var(--transition);
}

.contact-info:hover {
  transform: translateY(-8px)
}

.contact-info-icon i {
  font-size: 35px;
  color: var(--color-white);
  width: 70px;
  height: 70px;
  line-height: 70px;
  text-align: center;
  border-radius: 50px;
  background: var(--theme-color);
}

.contact-info h5 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 8px;
  color: var(--color-dark);
}

.contact-info p {
  color: var(--color-dark);
  font-weight: 500;
  font-size: 16px;
}


@media all and (max-width: 768px) {
  .contact-content {
    margin-top: 50px;
    margin-bottom: 0;
  }
}



/* ========================
41. Gallery css 
=========================== */

.gallery-item {
  position: relative;
  width: 100%;
  text-align: center;
}

.gallery-img {
  position: relative;
  height: 100%;
  border-radius: 15px;
  overflow: hidden;
}

.gallery-img::before {
  content: "";
  position: absolute;
  inset: 0;
  background: var(--theme-color);
  transition: all 0.7s ease-in-out;
  transform: translateY(-110%);
}

.gallery-img:hover::before {
  transform: translateY(110%);
}

.gallery-img::after {
  content: "";
  position: absolute;
  inset: 0;
  background: var(--theme-color);
  opacity: .8;
  transition: all 0.7s ease-in-out;
  transform: translateY(-110%);
}

.gallery-img:hover::after {
  transform: none;
}

.gallery-img img {
  width: 100%;
  border-radius: 15px;
}

.gallery-link {
  position: absolute;
  top: 50%;
  left: 50%;
  border-radius: 50%;
  font-size: 34px;
  color: var(--theme-color);
  width: 60px;
  height: 60px;
  line-height: 60px;
  background: var(--color-white);
  transition: all 200ms 0ms cubic-bezier(0.6, -0.28, 0.735, 0.045);
  transform: translate(-50%, -50%) scale(0);
  z-index: 1;
}

.gallery-item:hover .gallery-link {
  transform: translate(-50%, -50%) scale(1);
  transition: all 300ms 100ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transition-delay: 0.7s;
}



/*====================
42. Faq css 
======================*/

.faq-area .accordion-item {
  border: none;
  margin-bottom: 30px;
  background: var(--color-white);
  border-radius: 12px !important;
  box-shadow: var(--box-shadow);
}

.faq-area .accordion-item span {
  width: 45px;
  height: 45px;
  margin-right: 15px;
}

.faq-area .accordion-item i {
  width: 45px;
  height: 45px;
  line-height: 45px;
  border-radius: 50px;
  background: var(--theme-color);
  text-align: center;
  color: var(--color-white);
}

.faq-area .accordion-button:not(.collapsed) {
  color: var(--theme-color);
  background: transparent;
  box-shadow: inset 0 -1px 0 rgb(0 0 0 / 13%);
}

.faq-area .accordion-button {
  border-radius: 0px !important;
  background: transparent;
  font-weight: 700;
  font-size: 20px;
  color: var(--color-dark);
  box-shadow: none !important;
}

.faq-area .accordion-button:not(.collapsed) {
  border-bottom: 1px solid var(--theme-color);
}

.faq-area .accordion-button:not(.collapsed)::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.faq-area .accordion-body {
  color: var(--color-dark);
  font-weight: 500;
}

@media all and (max-width: 991px) {
  .faq-area .faq-right {
    margin-bottom: 50px;
  }

  .faq-area .accordion-button {
    font-size: 16px;
  }
}



/*====================
43. Breadcrumb css
======================*/

.site-breadcrumb {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  background-repeat: no-repeat !important;
  background-position: center !important;
  background-size: cover !important;
  position: relative;
  padding-top: 150px;
  padding-bottom: 100px;
  z-index: 1;
}

.site-breadcrumb::before {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  background: var(--hero-overlay);
  opacity: 0.65;
  z-index: -1;
}

.site-breadcrumb .breadcrumb-title {
  font-size: 40px;
  color: var(--color-white);
  font-weight: 700;
  margin-bottom: 10px;
  text-transform: capitalize;
  font-family: var(--heading-font2);
}

.site-breadcrumb .breadcrumb-menu {
  position: relative;
  z-index: 1;
}

.site-breadcrumb .breadcrumb-menu li {
  position: relative;
  display: inline-block;
  margin-left: 25px;
  color: var(--color-white);
  font-weight: 500;
  text-transform: capitalize;
}

.site-breadcrumb .breadcrumb-menu li a {
  color: var(--color-white);
  transition: all 0.5s ease-in-out;
}

.site-breadcrumb .breadcrumb-menu li::before {
  position: absolute;
  content: '\f101';
  font-family: 'Font Awesome 6 Pro';
  right: -21px;
  top: 1px;
  text-align: center;
  font-size: 16px;
  color: var(--color-white);
}

.site-breadcrumb .breadcrumb-menu li:first-child {
  margin-left: 0;
}

.site-breadcrumb .breadcrumb-menu li:last-child:before {
  display: none;
}

.site-breadcrumb .breadcrumb-menu li a:hover {
  color: var(--theme-color);
}

.site-breadcrumb .breadcrumb-menu li.active {
  color: var(--theme-color);
}




/*====================
44. Pagination css 
======================*/

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 50px;
}

.pagination .page-link {
  border: none;
  background: var(--theme-bg-light);
  color: var(--color-dark);
  font-weight: 500;
  margin: 0 10px;
  border-radius: 12px !important;
  width: 40px;
  height: 40px;
  line-height: 28px;
  text-align: center;
  transition: var(--transition);
  z-index: 1;
}

.pagination .page-link:hover,
.pagination .page-item.active .page-link {
  background: var(--theme-color);
  color: var(--color-white);
}



/*====================
45. Auth css 
======================*/

.login-form {
  padding: 40px;
  background: var(--theme-bg-light);
  border-radius: 15px;
}

.login-form .login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-form .login-header img {
  width: 200px;
  margin-bottom: 10px;
}

.login-form .login-header h3 {
  color: var(--theme-color);
  margin-bottom: 5px;
  font-weight: 800;
}

.login-form .login-header p {
  color: var(--color-dark);
  font-size: 20px;
}

.login-form .login-footer {
  margin-top: 25px;
}

.login-form .login-footer p {
  color: var(--color-dark);
  text-align: center;
}

.login-form .login-footer a {
  color: var(--theme-color);
  transition: .5s;
}

.login-form .login-footer a:hover {
  color: var(--theme-color2);
}

.login-form .input-group {
  margin-bottom: 22px;
}

.login-form label {
  color: var(--color-dark);
  margin-bottom: 5px;
}

.login-form .input-group:focus-within {
  outline: 2px solid var(--theme-color);
  border-radius: 50px;
}

.login-form .input-group .form-control {
  padding: 15px 25px 15px 0px;
  border-radius: 50px;
  border: none;
  box-shadow: none;
  background: var(--color-white);
}

.login-form .input-group .nice-select {
  line-height: 25px;
}

.login-form .input-group .form-control,
.login-form .input-group .form-select,
.login-form .input-group .form-control::placeholder {
  color: var(--color-dark);
}

.login-form .input-group .input-group-text {
  background: var(--color-white);
  color: var(--theme-color);
  border-radius: 50px;
  padding-left: 20px;
  border: none;
}

.login-form .form-check {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0;
  margin: 0;
}

.login-form .form-check-input {
  margin: 0;
  box-shadow: none;
  border-radius: 50px;
  border: none;
  width: 20px;
  height: 20px;
  background-color: var(--color-white);
}

.login-form .form-check-input:checked {
  background-color: var(--theme-color);
  border-color: var(--theme-color);
}

.login-form .form-check-label {
  margin-bottom: 0;
}

.login-form .form-check-label a {
  color: var(--theme-color);
  transition: var(--transition);
}

.login-form .form-check-label a:hover {
  color: var(--theme-color);
}

.login-form .forgot-password {
  color: var(--theme-color);
  transition: var(--transition);
}

.login-form .forgot-password:hover {
  color: var(--theme-color2);
}

.login-form .theme-btn {
  width: 100%;
}

.login-form .theme-btn::before {
  width: 420px;
  height: 420px;
  transition: var(--transition2);
}

.login-form .social-login {
  border-top: 1px solid var(--border-info-color);
  margin-top: 15px;
}

.login-form .social-login p {
  color: var(--color-dark);
  margin: 10px 0;
}

.login-form .social-login-list {
  text-align: center;
}

.login-form .social-login-list a {
  width: 40px;
  height: 40px;
  line-height: 40px;
  background: var(--theme-color);
  border-radius: 50px;
  margin: 5px;
}

.login-form .social-login-list a i {
  color: var(--color-white);
}

.login-form .social-login-list a:hover {
  background: var(--theme-color2);
}

@media only screen and (max-width: 991px) {
  .login-form {
    padding: 40px 20px;
  }
}




/*====================
46. Partner css 
======================*/

.partner-area {
  position: relative;
}

.partner-bg {
  position: relative;
  background: rgba(252, 34, 106, .03);
}

.partner-bg::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-image: url(../img/shape/04.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  opacity: 0.2;
}

.partner-wrapper {
  position: relative;
}




/*====================
47. Coming soon css 
======================*/

.coming-soon {
  position: relative;
  background-repeat: no-repeat;
  background-size: cover !important;
  background-position: center !important;
  min-height: 100vh;
  width: 100%;
  z-index: 1;
}

.coming-soon:before {
  position: absolute;
  content: '';
  background: rgba(0, 0, 0, .7);
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.coming-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 75vh;
}

.coming-content {
  text-align: center;
}

.coming-info h1 {
  color: var(--color-white);
  font-weight: 700;
  font-size: 50px;
}

.coming-info p {
  font-size: 18px;
  color: var(--color-white);
  margin-top: 10px;
}

.coming-countdown-wrap {
  margin-top: 30px;
  margin-bottom: 30px;
}

.coming-countdown .time-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 18px;
}

.coming-countdown .time {
  background: var(--color-white);
  width: 125px;
  border-radius: 15px;
  padding: 14px 10px;
}

.coming-countdown .time span {
  display: block;
}

.coming-countdown .time span:first-child {
  color: var(--theme-color);
  font-weight: 700;
  font-size: 38px;
  line-height: 1;
}

.coming-countdown .time .unit {
  color: var(--color-dark);
  font-weight: 500;
}

.coming-countdown .divider {
  display: none;
}

.coming-newsletter-form {
  margin-top: 50px;
  margin-bottom: 25px;
}

.coming-newsletter-form .form-group {
  position: relative;
}

.coming-newsletter-form .form-control {
  padding: 17px 150px 17px 20px;
  border-radius: 50px;
  border: none;
  box-shadow: none;
  color: var(--color-dark);
}

.coming-newsletter-form .form-control::placeholder {
  color: var(--color-dark);
}

.coming-newsletter-form .theme-btn {
  position: absolute;
  right: 5px;
  top: 5px;
  padding: 9px 15px;
}

.coming-social {
  margin-top: 40px;
  text-align: center;
}

.coming-social a {
  display: inline-block;
  background: var(--theme-color);
  color: var(--color-white);
  margin: 5px;
  width: 42px;
  height: 42px;
  line-height: 42px;
  text-align: center;
  border-radius: 50px;
  transition: .5s;
  box-shadow: var(--box-shadow2);
}

.coming-social a:hover {
  background: var(--theme-color2);
}

@media all and (max-width: 767px) {
  .coming-info h1 {
    font-size: 40px;
  }
}



/*====================
48. Error css 
======================*/

.error-wrapper {
  text-align: center;
}

.error-wrapper h1 {
  font-size: 250px;
  letter-spacing: 5px;
  font-weight: bold;
  color: var(--theme-color);
}

.error-wrapper h1 span {
  color: var(--color-dark);
}

.error-wrapper h2 {
  margin-top: 30px;
  margin-bottom: 10px;
}

.error-wrapper img {
  width: 100%;
}

.error-wrapper .theme-btn {
  margin-top: 30px;
}


@media all and (max-width: 767px) {
  .error-wrapper h1 {
    font-size: 160px;
  }
}



/*====================
49. Terms/privacy css 
====================== */

.terms-content:not(:last-child) {
  margin-bottom: 54px;
}

.terms-content:first-child {
  margin-top: -3px;
}

.terms-content .terms-list {
  margin-top: 37px;
}

.terms-content h3 {
  margin-bottom: 23px;
  position: relative;
}

.terms-content p:not(:last-child) {
  margin-bottom: 26px;
}

.terms-list li:not(:last-child) {
  margin-bottom: 16px;
}



/*====================
50. Footer css 
======================*/

.footer-area {
  background: var(--footer-bg);
  position: relative;
  z-index: 1;
}

.footer-shape img {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: .3;
  z-index: -1;
}

.footer-widget-box {
  margin-bottom: 20px;
}

.footer-widget-box.about-us {
  padding-right: 50px;
}

.footer-widget {
  position: relative;
  z-index: 1;
}

.footer-logo img {
  width: 220px;
  margin-bottom: 18px;
}

.footer-widget-title {
  color: var(--color-white);
  position: relative;
  padding-bottom: 20px;
  margin-bottom: 30px;
  font-size: 20px;
  z-index: 1;
}

.footer-widget-title::before {
  position: absolute;
  content: '';
  z-index: -1;
  width: 90px;
  height: 2px;
  background: rgba(255, 255, 255, 0.2);
  bottom: 0;
  left: 0;
}

.footer-widget-title::after {
  position: absolute;
  content: '';
  z-index: -1;
  width: 30px;
  height: 2px;
  background-color: var(--theme-color);
  bottom: 0;
  left: 18px;
}

.footer-list {
  display: flex;
  flex-direction: column;
  gap: .65rem;
}

.footer-list li a {
  color: var(--color-white);
  transition: var(--transition);
}

.footer-list li a i {
  margin-right: 5px;
  color: var(--theme-color);
}

.footer-list li a:hover {
  padding-left: 10px;
  color: var(--theme-color);
}

.footer-widget-box p {
  color: var(--color-white);
  padding-right: 18px;
  margin-bottom: 20px;
}

.footer-widget-box .social i {
  width: 20px;
}

.footer-social {
  display: flex;
  gap: 15px;
  justify-content: end;
}

.footer-social li a i {
  height: 38px;
  width: 38px;
  line-height: 38px;
  text-align: center;
  border-radius: 50px;
  background: var(--color-white);
  color: var(--theme-color);
  transition: var(--transition);
}

.footer-social li a i:hover {
  background: var(--color-dark);
  color: var(--color-white);
}

.footer-contact li {
  position: relative;
  display: flex;
  justify-content: start;
  align-items: center;
  color: var(--footer-text-color);
  font-size: 16px;
  margin-bottom: 12px;
}

.footer-contact li a {
  color: var(--footer-text-color);
  transition: var(--transition);
}

.footer-contact li i {
  width: 32px;
  height: 32px;
  line-height: 32px;
  font-size: 16px;
  margin-right: 10px;
  border-radius: 50px;
  background: var(--theme-color);
  text-align: center;
  transition: var(--transition);
  color: var(--color-white);
}

.footer-request {
  margin-top: 20px;
}

.footer-request p {
  font-weight: 500;
  margin-bottom: 12px;
  letter-spacing: 4px;
}

.footer-request .theme-btn i {
  margin-left: 12px;
}

.subscribe-form .form-group {
  position: relative;
}

.subscribe-form .form-control {
  padding: 16px 140px 16px 25px;
  border-radius: 50px;
  color: var(--color-dark);
  box-shadow: none;
  border: none;
}

.subscribe-form .form-control:focus {
  outline: 2px solid var(--theme-color);
}

.subscribe-form .form-control::placeholder {
  color: var(--color-dark);
}

.subscribe-form .theme-btn {
  position: absolute;
  padding: 10px 18px;
  right: 4px;
  top: 3px;
}

.subscribe-form .theme-btn:hover {
  color: var(--color-white);
}

.subscribe-form .theme-btn::before {
  background: var(--theme-color2);
}

.footer-widget-box.about-us .footer-newsletter p {
  font-size: 18px;
  font-weight: 500;
}

.footer-menu {
  margin: 0;
  padding: 0;
  text-align: right;
}

.footer-menu li {
  position: relative;
  display: inline-block;
  margin-left: 25px;
  font-size: 16px;
}

.footer-menu li::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  background: var(--theme-color);
  right: -18px;
  top: 13px;
  border-radius: 50px;
}

.footer-menu li:last-child::after {
  display: none;
}

.footer-menu li a {
  color: var(--footer-text-color);
  transition: var(--transition);
}

.footer-menu li a:hover {
  color: var(--theme-color);
}

.copyright {
  padding: 20px 0;
  border-top: 1px solid var(--border-white-color);
}

.copyright .copyright-text {
  color: var(--footer-text-color);
  margin-bottom: 0px;
  font-size: 16px;
}

.copyright .copyright-text a {
  color: var(--theme-color);
  font-weight: 500;
}


@media all and (max-width: 1199px) {
  .footer-widget-box {
    margin-bottom: 50px;
  }
}

@media all and (max-width: 991px) {
  .footer-widget-wrapper {
    padding-bottom: 0px;
  }

  .footer-menu {
    float: left;
    margin-top: 20px;
    text-align: left;
  }

  .footer-menu li {
    margin-left: 0;
    margin-right: 15px;
  }

  .footer-menu li::after {
    right: -12px;
  }
}

@media all and (max-width: 767px) {
  .footer-widget-wrapper {
    padding-bottom: 0px;
  }

  .footer-social {
    justify-content: flex-start;
    margin-top: 20px;
  }
}




/*====================
51. Home 2
======================*/

.home-2 .header {
  background: transparent;
}

.home-2 .navbar::before {
  display: none;
}

.home-2 .main {
  margin-top: -9rem;
}

.home-2 .hero-single {
  padding-top: 220px;
  padding-bottom: 120px;
}

.home-2 .hero-single::before {
  opacity: .85;
}

.home-2 .navbar-brand img {
  width: 190px;
}

.home-2 .hero-img-wrap {
  position: relative;
}

.home-2 .hero-img {
  text-align: end;
  position: relative;
}

.home-2 .hero-img::before {
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  background: var(--theme-gradient);
  width: 80%;
  height: 100%;
  transform: rotate(10deg);
  border-radius: 50px;
  z-index: -1;
}

.home-2 .hero-img img {
  border-radius: 15px 100px 15px 100px;
  width: 90%;
}

.home-2 .hero-img-wrap .event-countdown.ec-1 {
  margin-top: -150px;
  border-radius: 100px 0 0 100px;
}

.home-2 .hero-shape {
  position: absolute;
  left: 0;
  top: 140px;
  width: 350px;
  opacity: .15;
  z-index: -1;
}


@media all and (min-width: 992px) {
  .home-2 .navbar .nav-item .nav-link {
    color: var(--color-white);
  }

  .home-2 .navbar.fixed-top .nav-item .nav-link {
    color: var(--color-dark);
  }

  .home-2 .nav-right .nav-right-link {
    color: var(--color-white);
  }

  .home-2 .navbar.fixed-top .nav-right .nav-right-link {
    color: var(--color-dark);
  }
}

@media all and (max-width: 991px) {
  .home-2 .navbar {
    background: transparent;
  }

  .home-2 .navbar.fixed-top {
    background: var(--color-white);
  }

  .home-2 .hero-single {
    padding-top: 320px;
    padding-bottom: 150px;
  }

  .home-2 .mobile-menu-right .nav-right-link {
    color: var(--color-white);
  }

  .home-2 .navbar-toggler-mobile-icon {
    color: var(--color-white);
  }

  .home-2 .navbar.fixed-top .mobile-menu-right .nav-right-link,
  .home-2 .navbar.fixed-top .navbar-toggler-mobile-icon {
    color: var(--color-dark);
  }

  .home-2 .hero-img {
    margin-top: 80px;
  }

  .home-2 .hero-img img {
    width: 95%;
  }

  .home-2 .event-countdown.ec-1 .time-wrap {
    gap: 2px;
  }

  .home-2 .event-countdown.ec-1 {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}



/*====================
52. Home 3
======================*/

.home-3 .header {
  background: transparent;
}

.home-3 .header-top {
  background: var(--theme-color2);
}

.home-3 .navbar::before {
  display: none;
}

.home-3 .main {
  margin-top: 0;
}

.home-3 .hero-single .hero-date {
  justify-content: center;
}

.b2b h4 {
  font-size: 20px;
}

.explore .schedule-content {
  border-right: 0px;
}

.explore .schedule-bottom {
  margin-top: 15px;
  justify-content: right;
}